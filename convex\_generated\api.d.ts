/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as accountsPayable from "../accountsPayable.js";
import type * as balanceSheet from "../balanceSheet.js";
import type * as cashBank from "../cashBank.js";
import type * as cashFlowStatement from "../cashFlowStatement.js";
import type * as chartOfAccounts from "../chartOfAccounts.js";
import type * as generalLedger from "../generalLedger.js";
import type * as incomeStatement from "../incomeStatement.js";
import type * as reportSnapshots from "../reportSnapshots.js";
import type * as systemInit from "../systemInit.js";
import type * as trialBalance from "../trialBalance.js";
import type * as utils_accounting from "../utils/accounting.js";
import type * as utils_pfrsValidation from "../utils/pfrsValidation.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  accountsPayable: typeof accountsPayable;
  balanceSheet: typeof balanceSheet;
  cashBank: typeof cashBank;
  cashFlowStatement: typeof cashFlowStatement;
  chartOfAccounts: typeof chartOfAccounts;
  generalLedger: typeof generalLedger;
  incomeStatement: typeof incomeStatement;
  reportSnapshots: typeof reportSnapshots;
  systemInit: typeof systemInit;
  trialBalance: typeof trialBalance;
  "utils/accounting": typeof utils_accounting;
  "utils/pfrsValidation": typeof utils_pfrsValidation;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
