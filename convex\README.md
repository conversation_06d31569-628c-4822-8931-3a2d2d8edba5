# Philippine GAAP/PFRS Compliant Accounting System

A comprehensive double-entry accounting system built with Convex, designed specifically for Philippine businesses with full PFRS (Philippine Financial Reporting Standards) and BIR (Bureau of Internal Revenue) compliance.

## 🏗️ System Architecture

### Core Modules

1. **Chart of Accounts** (`chartOfAccounts.js`)
2. **General Ledger** (`generalLedger.js`)
3. **Trial Balance** (`trialBalance.js`)
4. **Balance Sheet** (`balanceSheet.js`)
5. **Income Statement** (`incomeStatement.js`)
6. **Cash Flow Statement** (`cashFlowStatement.js`)
7. **Accounts Payable** (`accountsPayable.js`)
8. **Cash & Bank Management** (`cashBank.js`)
9. **Report Snapshots** (`reportSnapshots.js`)
10. **System Initialization** (`systemInit.js`)

### Utility Modules

- **Accounting Utils** (`utils/accounting.js`) - Core accounting calculations and validations
- **PFRS Validation** (`utils/pfrsValidation.js`) - Philippine Financial Reporting Standards compliance

## 🚀 Quick Start

### 1. Initialize the System

```javascript
// Initialize with Philippine GAAP/PFRS compliant chart of accounts
await initializeAccountingSystem({
  companyName: "Your Company Name",
  fiscalYearEnd: "12-31", // December 31st
  baseCurrency: "PHP",
  createdBy: "admin"
});
```

### 2. Create Accounts

```javascript
// Create a new account
await createAccount({
  code: "1004",
  name: "Petty Cash Fund",
  type: "asset",
  subtype: "current_asset",
  description: "Petty cash for small expenses"
});
```

### 3. Post Journal Entries

```javascript
// Post a journal entry with PFRS validation
await postJournalEntry({
  date: Date.now(),
  description: "Sale of goods to customer",
  reference: "INV-001",
  lines: [
    {
      accountId: "cash_account_id",
      debit: 11200,
      credit: 0,
      description: "Cash received from customer"
    },
    {
      accountId: "sales_account_id",
      debit: 0,
      credit: 10000,
      description: "Sales revenue"
    },
    {
      accountId: "vat_payable_id",
      debit: 0,
      credit: 1200,
      description: "12% VAT on sales"
    }
  ]
});
```

## 📊 Financial Reports

### Balance Sheet (Statement of Financial Position)

```javascript
// Generate balance sheet as of specific date
const balanceSheet = await generateBalanceSheet({
  asOfDate: Date.now(),
  includeZeroBalances: false
});

// Generate comparative balance sheet
const comparative = await generateComparativeBalanceSheet({
  currentDate: Date.now(),
  priorDate: Date.now() - (365 * 24 * 60 * 60 * 1000) // 1 year ago
});
```

### Income Statement (Statement of Comprehensive Income)

```javascript
// Generate income statement for period
const incomeStatement = await generateIncomeStatement({
  startDate: startOfYear,
  endDate: endOfYear,
  includeZeroBalances: false
});

// Generate comparative income statement
const comparative = await generateComparativeIncomeStatement({
  currentStartDate: currentYearStart,
  currentEndDate: currentYearEnd,
  priorStartDate: priorYearStart,
  priorEndDate: priorYearEnd
});
```

### Cash Flow Statement (Indirect Method)

```javascript
// Generate cash flow statement using indirect method
const cashFlow = await generateCashFlowStatement({
  startDate: startOfYear,
  endDate: endOfYear
});
```

### Trial Balance

```javascript
// Generate trial balance
const trialBalance = await generateTrialBalance({
  startDate: startOfYear,
  endDate: endOfYear,
  includeZeroBalances: false
});

// Generate adjusted trial balance
const adjustedTB = await generateAdjustedTrialBalance({
  startDate: startOfYear,
  endDate: endOfYear
});
```

## 💰 Accounts Payable Management

### Record Supplier Invoice

```javascript
await recordPayable({
  vendorName: "ABC Supplier",
  invoiceNumber: "INV-2024-001",
  invoiceDate: Date.now(),
  dueDate: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days
  amount: 50000,
  terms: "Net 30",
  accountsPayableAccountId: "ap_account_id",
  expenseAccountId: "expense_account_id"
});
```

### Apply Payment

```javascript
await applyPaymentToPayable({
  payableId: "payable_id",
  amount: 50000,
  paymentDate: Date.now(),
  paymentMethod: "Bank Transfer",
  cashAccountId: "bank_account_id",
  accountsPayableAccountId: "ap_account_id"
});
```

### Aging Schedule

```javascript
const aging = await getAgingSchedule({
  asOfDate: Date.now(),
  includeZeroBalances: false
});
```

## 💵 Cash & Bank Management

### Record Cash Transaction

```javascript
await recordCashTransaction({
  type: "inflow", // or "outflow"
  accountId: "cash_account_id",
  amount: 25000,
  description: "Cash sales",
  counterAccountId: "sales_account_id",
  date: Date.now()
});
```

### Bank Reconciliation

```javascript
const reconciliation = await getBankReconciliation({
  bankAccountId: "bank_account_id",
  statementDate: Date.now(),
  statementBalance: 150000
});
```

## 📸 Report Snapshots

### Create Snapshots

```javascript
// Snapshot balance sheet
await snapshotBalanceSheet({
  asOfDate: Date.now(),
  generatedBy: "admin",
  description: "Month-end balance sheet"
});

// Snapshot income statement
await snapshotIncomeStatement({
  startDate: startOfMonth,
  endDate: endOfMonth,
  generatedBy: "admin"
});
```

### Compare Snapshots

```javascript
const comparison = await compareReportSnapshots({
  currentSnapshotId: "current_snapshot_id",
  priorSnapshotId: "prior_snapshot_id"
});
```

## ✅ PFRS Compliance Features

### Automatic Validations

- **PFRS 15**: Revenue recognition validation
- **PAS 1**: Financial statement presentation compliance
- **PAS 7**: Cash flow classification suggestions
- **PAS 16**: Property, plant & equipment recognition

### BIR Compliance

- Philippine chart of accounts structure (1000-9999)
- Proper audit trail with references
- VAT and withholding tax calculations
- Books of accounts summary generation

## 🔧 System Configuration

### Check System Status

```javascript
const status = await getSystemStatus();
console.log(status.isInitialized); // true/false
console.log(status.pfrsCompliance); // true/false
console.log(status.statistics); // Account counts, etc.
```

### Account Validation

All accounts follow Philippine BIR chart of accounts structure:
- **1000-2999**: Assets
- **3000-4999**: Liabilities
- **5000-5999**: Equity
- **6000-6999**: Revenue
- **7000-9999**: Expenses

## 📋 Data Schema

### Key Tables

- `accounts` - Chart of accounts
- `journal_entries` - Journal entry headers
- `journal_entry_lines` - Journal entry line items
- `accounts_payable` - Supplier invoices
- `ap_payments` - AP payment records
- `cash_bank_accounts` - Cash/bank account setup
- `report_snapshots` - Saved financial reports
- `accounting_settings` - System configuration

## 🛡️ Security & Validation

- Double-entry accounting enforcement
- Account type validation
- Date range validation
- Amount precision (2 decimal places)
- Circular reference prevention
- PFRS compliance checks
- BIR audit trail requirements

## 📈 Financial Ratios

Automatically calculated ratios include:
- Current Ratio
- Debt-to-Equity Ratio
- Working Capital
- Gross Profit Margin
- Operating Profit Margin
- Net Profit Margin

## 🔄 Integration Points

The system is designed to integrate with:
- Philippine tax software
- BIR electronic filing systems
- Banking APIs for reconciliation
- Payroll systems
- Inventory management systems

## 📞 Support

For technical support or customization requests, please refer to the system documentation or contact your development team.

---

**Note**: This system is designed specifically for Philippine businesses and follows local GAAP, PFRS, and BIR requirements. Ensure compliance with current regulations and consult with qualified accountants for complex transactions.
