import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

// TypeScript interfaces for accounting data
interface Account {
  _id: string;
  code: string;
  name: string;
  type: "asset" | "liability" | "equity" | "revenue" | "expense";
  subtype?: string;
  isActive: boolean;
}

interface AccountBalance {
  account: Account;
  balance: number;
  totalDebits: number;
  totalCredits: number;
  classification: string;
}

export default function AccountingDashboard() {
  const [selectedReport, setSelectedReport] = useState("balance_sheet");
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });

  // System status
  const systemStatus = useQuery(api.systemInit.getSystemStatus);
  
  // Mutations
  const initializeSystem = useMutation(api.systemInit.initializeAccountingSystem);
  const createAccount = useMutation(api.chartOfAccounts.createAccount);
  const postJournalEntry = useMutation(api.generalLedger.postJournalEntry);

  // Queries for reports
  const balanceSheet = useQuery(
    api.balanceSheet.generateBalanceSheet,
    selectedReport === "balance_sheet" ? {
      asOfDate: new Date(dateRange.endDate).getTime(),
      includeZeroBalances: false
    } : "skip"
  );

  const incomeStatement = useQuery(
    api.incomeStatement.generateIncomeStatement,
    selectedReport === "income_statement" ? {
      startDate: new Date(dateRange.startDate).getTime(),
      endDate: new Date(dateRange.endDate).getTime(),
      includeZeroBalances: false
    } : "skip"
  );

  const trialBalance = useQuery(
    api.trialBalance.generateTrialBalance,
    selectedReport === "trial_balance" ? {
      startDate: new Date(dateRange.startDate).getTime(),
      endDate: new Date(dateRange.endDate).getTime(),
      includeZeroBalances: false
    } : "skip"
  );

  const chartOfAccounts = useQuery(api.chartOfAccounts.getChartOfAccounts, {
    includeInactive: false
  });

  const handleInitializeSystem = async () => {
    try {
      await initializeSystem({
        companyName: "XYZ Finance Corporation",
        fiscalYearEnd: "12-31",
        baseCurrency: "PHP",
        createdBy: "admin"
      });
      alert("System initialized successfully!");
    } catch (error) {
      alert(`Error: ${error.message}`);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  if (!systemStatus) {
    return <div className="p-8">Loading system status...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Philippine GAAP/PFRS Accounting System
          </h1>
          <p className="text-gray-600">
            Comprehensive double-entry accounting with PFRS compliance
          </p>
        </div>

        {/* System Status */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">System Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-900">Initialization</h3>
              <p className={`text-sm ${systemStatus.isInitialized ? 'text-green-600' : 'text-red-600'}`}>
                {systemStatus.isInitialized ? '✅ Initialized' : '❌ Not Initialized'}
              </p>
              {!systemStatus.isInitialized && (
                <button
                  onClick={handleInitializeSystem}
                  className="mt-2 bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700"
                >
                  Initialize System
                </button>
              )}
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-medium text-green-900">PFRS Compliance</h3>
              <p className="text-sm text-green-600">
                {systemStatus.pfrsCompliance ? '✅ Enabled' : '❌ Disabled'}
              </p>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="font-medium text-purple-900">BIR Compliance</h3>
              <p className="text-sm text-purple-600">
                {systemStatus.birCompliance ? '✅ Enabled' : '❌ Disabled'}
              </p>
            </div>
          </div>
          
          {systemStatus.isInitialized && (
            <div className="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
              <div>
                <span className="font-medium">Company:</span>
                <p>{systemStatus.companyName}</p>
              </div>
              <div>
                <span className="font-medium">Total Accounts:</span>
                <p>{systemStatus.statistics.totalAccounts}</p>
              </div>
              <div>
                <span className="font-medium">Journal Entries:</span>
                <p>{systemStatus.statistics.totalJournalEntries}</p>
              </div>
              <div>
                <span className="font-medium">Currency:</span>
                <p>{systemStatus.baseCurrency}</p>
              </div>
              <div>
                <span className="font-medium">Fiscal Year End:</span>
                <p>{systemStatus.fiscalYearEnd}</p>
              </div>
            </div>
          )}
        </div>

        {systemStatus.isInitialized && (
          <>
            {/* Report Selection */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="text-xl font-semibold mb-4">Financial Reports</h2>
              
              <div className="flex flex-wrap gap-4 mb-4">
                <button
                  onClick={() => setSelectedReport("balance_sheet")}
                  className={`px-4 py-2 rounded ${
                    selectedReport === "balance_sheet"
                      ? "bg-blue-600 text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                  }`}
                >
                  Balance Sheet
                </button>
                <button
                  onClick={() => setSelectedReport("income_statement")}
                  className={`px-4 py-2 rounded ${
                    selectedReport === "income_statement"
                      ? "bg-blue-600 text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                  }`}
                >
                  Income Statement
                </button>
                <button
                  onClick={() => setSelectedReport("trial_balance")}
                  className={`px-4 py-2 rounded ${
                    selectedReport === "trial_balance"
                      ? "bg-blue-600 text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                  }`}
                >
                  Trial Balance
                </button>
                <button
                  onClick={() => setSelectedReport("chart_of_accounts")}
                  className={`px-4 py-2 rounded ${
                    selectedReport === "chart_of_accounts"
                      ? "bg-blue-600 text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                  }`}
                >
                  Chart of Accounts
                </button>
              </div>

              {/* Date Range Selector */}
              {["income_statement", "trial_balance"].includes(selectedReport) && (
                <div className="flex gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Start Date
                    </label>
                    <input
                      type="date"
                      value={dateRange.startDate}
                      onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                      className="border border-gray-300 rounded px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      End Date
                    </label>
                    <input
                      type="date"
                      value={dateRange.endDate}
                      onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                      className="border border-gray-300 rounded px-3 py-2"
                    />
                  </div>
                </div>
              )}

              {selectedReport === "balance_sheet" && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    As of Date
                  </label>
                  <input
                    type="date"
                    value={dateRange.endDate}
                    onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                    className="border border-gray-300 rounded px-3 py-2"
                  />
                </div>
              )}
            </div>

            {/* Report Display */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">
                {selectedReport === "balance_sheet" && "Balance Sheet (Statement of Financial Position)"}
                {selectedReport === "income_statement" && "Income Statement (Statement of Comprehensive Income)"}
                {selectedReport === "trial_balance" && "Trial Balance"}
                {selectedReport === "chart_of_accounts" && "Chart of Accounts"}
              </h2>

              {/* Balance Sheet Display */}
              {selectedReport === "balance_sheet" && balanceSheet && (
                <div className="space-y-6">
                  <div className="text-sm text-gray-600 mb-4">
                    As of {new Date(dateRange.endDate).toLocaleDateString()}
                  </div>
                  
                  {/* Assets */}
                  <div>
                    <h3 className="font-semibold text-lg mb-2">ASSETS</h3>
                    <div className="ml-4">
                      <h4 className="font-medium mb-2">Current Assets</h4>
                      {balanceSheet.balanceSheet.assets.current.map((account: AccountBalance) => (
                        <div key={account.account._id} className="flex justify-between py-1">
                          <span className="ml-4">{account.account.name}</span>
                          <span>{formatCurrency(account.balance)}</span>
                        </div>
                      ))}
                      <div className="flex justify-between py-1 font-medium border-t">
                        <span className="ml-4">Total Current Assets</span>
                        <span>{formatCurrency(balanceSheet.balanceSheet.totals.totalCurrentAssets)}</span>
                      </div>
                    </div>
                    
                    <div className="ml-4 mt-4">
                      <h4 className="font-medium mb-2">Non-Current Assets</h4>
                      {balanceSheet.balanceSheet.assets.nonCurrent.map((account: AccountBalance) => (
                        <div key={account.account._id} className="flex justify-between py-1">
                          <span className="ml-4">{account.account.name}</span>
                          <span>{formatCurrency(account.balance)}</span>
                        </div>
                      ))}
                      <div className="flex justify-between py-1 font-medium border-t">
                        <span className="ml-4">Total Non-Current Assets</span>
                        <span>{formatCurrency(balanceSheet.balanceSheet.totals.totalNonCurrentAssets)}</span>
                      </div>
                    </div>
                    
                    <div className="flex justify-between py-2 font-bold text-lg border-t-2 border-black">
                      <span>TOTAL ASSETS</span>
                      <span>{formatCurrency(balanceSheet.balanceSheet.totals.totalAssets)}</span>
                    </div>
                  </div>

                  {/* Liabilities and Equity */}
                  <div>
                    <h3 className="font-semibold text-lg mb-2">LIABILITIES AND EQUITY</h3>
                    
                    {/* Current Liabilities */}
                    <div className="ml-4">
                      <h4 className="font-medium mb-2">Current Liabilities</h4>
                      {balanceSheet.balanceSheet.liabilities.current.map((account: AccountBalance) => (
                        <div key={account.account._id} className="flex justify-between py-1">
                          <span className="ml-4">{account.account.name}</span>
                          <span>{formatCurrency(account.balance)}</span>
                        </div>
                      ))}
                      <div className="flex justify-between py-1 font-medium border-t">
                        <span className="ml-4">Total Current Liabilities</span>
                        <span>{formatCurrency(balanceSheet.balanceSheet.totals.totalCurrentLiabilities)}</span>
                      </div>
                    </div>

                    {/* Non-Current Liabilities */}
                    <div className="ml-4 mt-4">
                      <h4 className="font-medium mb-2">Non-Current Liabilities</h4>
                      {balanceSheet.balanceSheet.liabilities.nonCurrent.map((account: AccountBalance) => (
                        <div key={account.account._id} className="flex justify-between py-1">
                          <span className="ml-4">{account.account.name}</span>
                          <span>{formatCurrency(account.balance)}</span>
                        </div>
                      ))}
                      <div className="flex justify-between py-1 font-medium border-t">
                        <span className="ml-4">Total Non-Current Liabilities</span>
                        <span>{formatCurrency(balanceSheet.balanceSheet.totals.totalNonCurrentLiabilities)}</span>
                      </div>
                    </div>

                    <div className="flex justify-between py-1 font-medium border-t">
                      <span>Total Liabilities</span>
                      <span>{formatCurrency(balanceSheet.balanceSheet.totals.totalLiabilities)}</span>
                    </div>

                    {/* Equity */}
                    <div className="ml-4 mt-4">
                      <h4 className="font-medium mb-2">Equity</h4>
                      {balanceSheet.balanceSheet.equity.accounts.map((account: AccountBalance) => (
                        <div key={account.account._id} className="flex justify-between py-1">
                          <span className="ml-4">{account.account.name}</span>
                          <span>{formatCurrency(account.balance)}</span>
                        </div>
                      ))}
                      {balanceSheet.balanceSheet.equity.retainedEarnings !== 0 && (
                        <div className="flex justify-between py-1">
                          <span className="ml-4">Retained Earnings</span>
                          <span>{formatCurrency(balanceSheet.balanceSheet.equity.retainedEarnings)}</span>
                        </div>
                      )}
                      <div className="flex justify-between py-1 font-medium border-t">
                        <span className="ml-4">Total Equity</span>
                        <span>{formatCurrency(balanceSheet.balanceSheet.totals.equity)}</span>
                      </div>
                    </div>

                    <div className="flex justify-between py-2 font-bold text-lg border-t-2 border-black">
                      <span>TOTAL LIABILITIES AND EQUITY</span>
                      <span>{formatCurrency(balanceSheet.balanceSheet.totals.liabilitiesAndEquity)}</span>
                    </div>
                  </div>

                  {/* Financial Ratios */}
                  <div className="mt-6 p-4 bg-gray-50 rounded">
                    <h4 className="font-medium mb-2">Key Financial Ratios</h4>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Current Ratio:</span>
                        <p>{balanceSheet.ratios.currentRatio.toFixed(2)}</p>
                      </div>
                      <div>
                        <span className="font-medium">Working Capital:</span>
                        <p>{formatCurrency(balanceSheet.ratios.workingCapital)}</p>
                      </div>
                      <div>
                        <span className="font-medium">Debt-to-Equity:</span>
                        <p>{balanceSheet.ratios.debtToEquityRatio.toFixed(2)}</p>
                      </div>
                    </div>
                  </div>

                  {/* Validation Status */}
                  <div className="mt-4 p-4 bg-green-50 rounded">
                    <h4 className="font-medium mb-2">Validation Status</h4>
                    <p className={`text-sm ${balanceSheet.summary.isBalanced ? 'text-green-600' : 'text-red-600'}`}>
                      {balanceSheet.summary.isBalanced ? '✅ Balance Sheet is balanced' : '❌ Balance Sheet is out of balance'}
                    </p>
                    {!balanceSheet.summary.isBalanced && (
                      <p className="text-sm text-red-600">
                        Difference: {formatCurrency(balanceSheet.summary.difference)}
                      </p>
                    )}
                  </div>
                </div>
              )}

              {/* Chart of Accounts Display */}
              {selectedReport === "chart_of_accounts" && chartOfAccounts && (
                <div className="space-y-4">
                  {["asset", "liability", "equity", "revenue", "expense"].map((type) => {
                    const accounts = chartOfAccounts.filter(account => account.type === type);
                    if (accounts.length === 0) return null;
                    
                    return (
                      <div key={type}>
                        <h3 className="font-semibold text-lg mb-2 capitalize">
                          {type === "expense" ? "Expenses" : type + "s"}
                        </h3>
                        <div className="ml-4 space-y-1">
                          {accounts.map((account: Account) => (
                            <div key={account._id} className="flex justify-between items-center py-1 border-b border-gray-100">
                              <div>
                                <span className="font-medium">{account.code}</span>
                                <span className="ml-2">{account.name}</span>
                                {account.subtype && (
                                  <span className="ml-2 text-sm text-gray-500">({account.subtype.replace('_', ' ')})</span>
                                )}
                              </div>
                              <span className={`text-sm px-2 py-1 rounded ${
                                account.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}>
                                {account.isActive ? 'Active' : 'Inactive'}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}

              {/* Loading states */}
              {selectedReport === "balance_sheet" && !balanceSheet && (
                <div className="text-center py-8">Loading balance sheet...</div>
              )}
              {selectedReport === "income_statement" && !incomeStatement && (
                <div className="text-center py-8">Loading income statement...</div>
              )}
              {selectedReport === "trial_balance" && !trialBalance && (
                <div className="text-center py-8">Loading trial balance...</div>
              )}
              {selectedReport === "chart_of_accounts" && !chartOfAccounts && (
                <div className="text-center py-8">Loading chart of accounts...</div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
