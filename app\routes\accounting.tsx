import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import DynamicSidebar from "../sidebar/DynamicSidebar";
import Navbar from "./finance/navbar/navbar";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  FileText,
  AlertCircle,
  CheckCircle,
  Factory,
  Package,
  Truck,
  Users,
  Calendar,
  PieChart
} from "lucide-react";

// TypeScript interfaces for accounting data
interface Account {
  _id: string;
  code: string;
  name: string;
  type: "asset" | "liability" | "equity" | "revenue" | "expense";
  subtype?: string;
  isActive: boolean;
}

interface AccountBalance {
  account: Account;
  balance: number;
  totalDebits: number;
  totalCredits: number;
  classification: string;
}

// System Status interface
interface SystemStatus {
  isInitialized: boolean;
  companyName: string | null;
  fiscalYearEnd: string;
  baseCurrency: string;
  pfrsCompliance: boolean;
  birCompliance: boolean;
  statistics: {
    totalAccounts: number;
    activeAccounts: number;
    totalJournalEntries: number;
    postedEntries: number;
    accountsByType: {
      assets: number;
      liabilities: number;
      equity: number;
      revenue: number;
      expenses: number;
    };
  };
}

// Balance Sheet interface
interface BalanceSheetData {
  balanceSheet: {
    assets: {
      current: AccountBalance[];
      nonCurrent: AccountBalance[];
    };
    liabilities: {
      current: AccountBalance[];
      nonCurrent: AccountBalance[];
    };
    equity: {
      accounts: AccountBalance[];
      retainedEarnings: number;
    };
    totals: {
      totalCurrentAssets: number;
      totalNonCurrentAssets: number;
      totalAssets: number;
      totalCurrentLiabilities: number;
      totalNonCurrentLiabilities: number;
      totalLiabilities: number;
      equity: number;
      liabilitiesAndEquity: number;
    };
  };
  ratios: {
    currentRatio: number;
    workingCapital: number;
    debtToEquityRatio: number;
  };
  summary: {
    isBalanced: boolean;
    difference: number;
  };
}

// Income Statement interface
interface IncomeStatementData {
  incomeStatement: {
    revenue: {
      operating: AccountBalance[];
      other: AccountBalance[];
      total: number;
    };
    expenses: {
      costOfSales: AccountBalance[];
      operating: AccountBalance[];
      administrative: AccountBalance[];
      finance: AccountBalance[];
      other: AccountBalance[];
      total: number;
    };
  };
}

// Trial Balance interface
interface TrialBalanceData {
  trialBalance: AccountBalance[];
  summary: {
    totalDebits: number;
    totalCredits: number;
    isBalanced: boolean;
    difference: number;
  };
}

// Account type for filtering
type AccountType = "asset" | "liability" | "equity" | "revenue" | "expense";

// Date range state interface
interface DateRange {
  startDate: string;
  endDate: string;
}

export default function AccountingDashboard() {
  const [selectedReport, setSelectedReport] = useState<string>("dashboard");
  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0]!,
    endDate: new Date().toISOString().split('T')[0]!
  });

  // System status
  const systemStatus = useQuery(api.systemInit.getSystemStatus) as SystemStatus | undefined;

  // Mutations
  const initializeSystem = useMutation(api.systemInit.initializeAccountingSystem);

  // Queries for reports
  const balanceSheet = useQuery(
    api.balanceSheet.generateBalanceSheet,
    selectedReport === "balance_sheet" ? {
      asOfDate: new Date(dateRange.endDate).getTime(),
      includeZeroBalances: false
    } : "skip"
  ) as BalanceSheetData | undefined;

  const incomeStatement = useQuery(
    api.incomeStatement.generateIncomeStatement,
    selectedReport === "income_statement" ? {
      startDate: new Date(dateRange.startDate).getTime(),
      endDate: new Date(dateRange.endDate).getTime(),
      includeZeroBalances: false
    } : "skip"
  ) as IncomeStatementData | undefined;

  const trialBalance = useQuery(
    api.trialBalance.generateTrialBalance,
    selectedReport === "trial_balance" ? {
      startDate: new Date(dateRange.startDate).getTime(),
      endDate: new Date(dateRange.endDate).getTime(),
      includeZeroBalances: false
    } : "skip"
  ) as TrialBalanceData | undefined;

  const chartOfAccounts = useQuery(api.chartOfAccounts.getChartOfAccounts, {
    includeInactive: false
  }) as Account[] | undefined;

  const handleInitializeSystem = async (): Promise<void> => {
    try {
      await initializeSystem({
        companyName: "XYZ Finance Corporation",
        fiscalYearEnd: "12-31",
        baseCurrency: "PHP",
        createdBy: "admin"
      });
      alert("System initialized successfully!");
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      alert(`Error: ${errorMessage}`);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  if (!systemStatus) {
    return (
      <div className="flex h-screen bg-gray-50">
        <DynamicSidebar />
        <div className="flex-1 flex flex-col">
          <Navbar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading XYZ Manufacturing ERP...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <DynamicSidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <div className="flex-1 overflow-auto">
          <div className="p-6">
            {/* Manufacturing ERP Header */}
            <div className="mb-6">
              <div className="flex items-center space-x-3 mb-2">
                <Factory className="h-8 w-8 text-blue-600" />
                <h1 className="text-3xl font-bold text-gray-900">
                  XYZ Manufacturing ERP
                </h1>
              </div>
              <p className="text-gray-600">
                Integrated Manufacturing & Financial Management System
              </p>
            </div>

            {/* Manufacturing Dashboard Overview */}
            {selectedReport === "dashboard" && (
              <div className="space-y-6">
                {/* Key Metrics Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="bg-white rounded-lg shadow-md p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                        <p className="text-2xl font-bold text-gray-900">₱2,450,000</p>
                        <p className="text-sm text-green-600 flex items-center">
                          <TrendingUp className="h-4 w-4 mr-1" />
                          +12.5% from last month
                        </p>
                      </div>
                      <DollarSign className="h-8 w-8 text-green-600" />
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow-md p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Manufacturing Costs</p>
                        <p className="text-2xl font-bold text-gray-900">₱1,680,000</p>
                        <p className="text-sm text-red-600 flex items-center">
                          <TrendingDown className="h-4 w-4 mr-1" />
                          +3.2% from last month
                        </p>
                      </div>
                      <Factory className="h-8 w-8 text-blue-600" />
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow-md p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Inventory Value</p>
                        <p className="text-2xl font-bold text-gray-900">₱890,000</p>
                        <p className="text-sm text-blue-600 flex items-center">
                          <Package className="h-4 w-4 mr-1" />
                          Current stock level
                        </p>
                      </div>
                      <Package className="h-8 w-8 text-purple-600" />
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow-md p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Accounts Payable</p>
                        <p className="text-2xl font-bold text-gray-900">₱320,000</p>
                        <p className="text-sm text-orange-600 flex items-center">
                          <AlertCircle className="h-4 w-4 mr-1" />
                          15 pending invoices
                        </p>
                      </div>
                      <FileText className="h-8 w-8 text-orange-600" />
                    </div>
                  </div>
                </div>

                {/* System Status */}
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-xl font-semibold mb-4 flex items-center">
                    <CheckCircle className="h-6 w-6 text-green-600 mr-2" />
                    System Status
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h3 className="font-medium text-blue-900">ERP System</h3>
                      <p className={`text-sm ${systemStatus.isInitialized ? 'text-green-600' : 'text-red-600'}`}>
                        {systemStatus.isInitialized ? '✅ Operational' : '❌ Not Initialized'}
                      </p>
                      {!systemStatus.isInitialized && (
                        <button
                          type="button"
                          onClick={handleInitializeSystem}
                          className="mt-2 bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700"
                        >
                          Initialize System
                        </button>
                      )}
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <h3 className="font-medium text-green-900">PFRS Compliance</h3>
                      <p className="text-sm text-green-600">
                        {systemStatus.pfrsCompliance ? '✅ Compliant' : '❌ Non-Compliant'}
                      </p>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <h3 className="font-medium text-purple-900">BIR Compliance</h3>
                      <p className="text-sm text-purple-600">
                        {systemStatus.birCompliance ? '✅ Compliant' : '❌ Non-Compliant'}
                      </p>
                    </div>
                  </div>

                  {systemStatus.isInitialized && (
                    <div className="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Company:</span>
                        <p>{systemStatus.companyName}</p>
                      </div>
                      <div>
                        <span className="font-medium">Total Accounts:</span>
                        <p>{systemStatus.statistics.totalAccounts}</p>
                      </div>
                      <div>
                        <span className="font-medium">Journal Entries:</span>
                        <p>{systemStatus.statistics.totalJournalEntries}</p>
                      </div>
                      <div>
                        <span className="font-medium">Currency:</span>
                        <p>{systemStatus.baseCurrency}</p>
                      </div>
                      <div>
                        <span className="font-medium">Fiscal Year End:</span>
                        <p>{systemStatus.fiscalYearEnd}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Manufacturing Navigation */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <PieChart className="h-6 w-6 text-blue-600 mr-2" />
                Manufacturing Financial Management
              </h2>

              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <button
                  type="button"
                  onClick={() => setSelectedReport("dashboard")}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    selectedReport === "dashboard"
                      ? "border-blue-600 bg-blue-50 text-blue-700"
                      : "border-gray-200 hover:border-gray-300 text-gray-700"
                  }`}
                >
                  <Factory className="h-6 w-6 mx-auto mb-2" />
                  <span className="text-sm font-medium">Dashboard</span>
                </button>

                <button
                  type="button"
                  onClick={() => setSelectedReport("balance_sheet")}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    selectedReport === "balance_sheet"
                      ? "border-blue-600 bg-blue-50 text-blue-700"
                      : "border-gray-200 hover:border-gray-300 text-gray-700"
                  }`}
                >
                  <FileText className="h-6 w-6 mx-auto mb-2" />
                  <span className="text-sm font-medium">Balance Sheet</span>
                </button>

                <button
                  type="button"
                  onClick={() => setSelectedReport("income_statement")}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    selectedReport === "income_statement"
                      ? "border-blue-600 bg-blue-50 text-blue-700"
                      : "border-gray-200 hover:border-gray-300 text-gray-700"
                  }`}
                >
                  <TrendingUp className="h-6 w-6 mx-auto mb-2" />
                  <span className="text-sm font-medium">P&L Statement</span>
                </button>

                <button
                  type="button"
                  onClick={() => setSelectedReport("trial_balance")}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    selectedReport === "trial_balance"
                      ? "border-blue-600 bg-blue-50 text-blue-700"
                      : "border-gray-200 hover:border-gray-300 text-gray-700"
                  }`}
                >
                  <DollarSign className="h-6 w-6 mx-auto mb-2" />
                  <span className="text-sm font-medium">Trial Balance</span>
                </button>

                <button
                  type="button"
                  onClick={() => setSelectedReport("chart_of_accounts")}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    selectedReport === "chart_of_accounts"
                      ? "border-blue-600 bg-blue-50 text-blue-700"
                      : "border-gray-200 hover:border-gray-300 text-gray-700"
                  }`}
                >
                  <Package className="h-6 w-6 mx-auto mb-2" />
                  <span className="text-sm font-medium">Chart of Accounts</span>
                </button>

                <button
                  type="button"
                  onClick={() => setSelectedReport("manufacturing_costs")}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    selectedReport === "manufacturing_costs"
                      ? "border-blue-600 bg-blue-50 text-blue-700"
                      : "border-gray-200 hover:border-gray-300 text-gray-700"
                  }`}
                >
                  <Truck className="h-6 w-6 mx-auto mb-2" />
                  <span className="text-sm font-medium">Mfg. Costs</span>
                </button>
              </div>

              {/* Date Range Selector */}
              {["income_statement", "trial_balance"].includes(selectedReport) && (
                <div className="flex gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Start Date
                    </label>
                    <input
                      type="date"
                      value={dateRange.startDate}
                      onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                      className="border border-gray-300 rounded px-3 py-2"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      End Date
                    </label>
                    <input
                      type="date"
                      value={dateRange.endDate}
                      onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                      className="border border-gray-300 rounded px-3 py-2"
                    />
                  </div>
                </div>
              )}

              {selectedReport === "balance_sheet" && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    As of Date
                  </label>
                  <input
                    type="date"
                    value={dateRange.endDate}
                    onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                    className="border border-gray-300 rounded px-3 py-2"
                  />
                </div>
              )}
            </div>

            {/* Report Display */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">
                {selectedReport === "balance_sheet" && "Balance Sheet (Statement of Financial Position)"}
                {selectedReport === "income_statement" && "Income Statement (Statement of Comprehensive Income)"}
                {selectedReport === "trial_balance" && "Trial Balance"}
                {selectedReport === "chart_of_accounts" && "Chart of Accounts"}
              </h2>

              {/* Balance Sheet Display */}
              {selectedReport === "balance_sheet" && balanceSheet && (
                <div className="space-y-6">
                  <div className="text-sm text-gray-600 mb-4">
                    As of {new Date(dateRange.endDate).toLocaleDateString()}
                  </div>
                  
                  {/* Assets */}
                  <div>
                    <h3 className="font-semibold text-lg mb-2">ASSETS</h3>
                    <div className="ml-4">
                      <h4 className="font-medium mb-2">Current Assets</h4>
                      {balanceSheet.balanceSheet.assets.current.map((account: AccountBalance) => (
                        <div key={account.account._id} className="flex justify-between py-1">
                          <span className="ml-4">{account.account.name}</span>
                          <span>{formatCurrency(account.balance)}</span>
                        </div>
                      ))}
                      <div className="flex justify-between py-1 font-medium border-t">
                        <span className="ml-4">Total Current Assets</span>
                        <span>{formatCurrency(balanceSheet.balanceSheet.totals.totalCurrentAssets)}</span>
                      </div>
                    </div>
                    
                    <div className="ml-4 mt-4">
                      <h4 className="font-medium mb-2">Non-Current Assets</h4>
                      {balanceSheet.balanceSheet.assets.nonCurrent.map((account: AccountBalance) => (
                        <div key={account.account._id} className="flex justify-between py-1">
                          <span className="ml-4">{account.account.name}</span>
                          <span>{formatCurrency(account.balance)}</span>
                        </div>
                      ))}
                      <div className="flex justify-between py-1 font-medium border-t">
                        <span className="ml-4">Total Non-Current Assets</span>
                        <span>{formatCurrency(balanceSheet.balanceSheet.totals.totalNonCurrentAssets)}</span>
                      </div>
                    </div>
                    
                    <div className="flex justify-between py-2 font-bold text-lg border-t-2 border-black">
                      <span>TOTAL ASSETS</span>
                      <span>{formatCurrency(balanceSheet.balanceSheet.totals.totalAssets)}</span>
                    </div>
                  </div>

                  {/* Liabilities and Equity */}
                  <div>
                    <h3 className="font-semibold text-lg mb-2">LIABILITIES AND EQUITY</h3>
                    
                    {/* Current Liabilities */}
                    <div className="ml-4">
                      <h4 className="font-medium mb-2">Current Liabilities</h4>
                      {balanceSheet.balanceSheet.liabilities.current.map((account: AccountBalance) => (
                        <div key={account.account._id} className="flex justify-between py-1">
                          <span className="ml-4">{account.account.name}</span>
                          <span>{formatCurrency(account.balance)}</span>
                        </div>
                      ))}
                      <div className="flex justify-between py-1 font-medium border-t">
                        <span className="ml-4">Total Current Liabilities</span>
                        <span>{formatCurrency(balanceSheet.balanceSheet.totals.totalCurrentLiabilities)}</span>
                      </div>
                    </div>

                    {/* Non-Current Liabilities */}
                    <div className="ml-4 mt-4">
                      <h4 className="font-medium mb-2">Non-Current Liabilities</h4>
                      {balanceSheet.balanceSheet.liabilities.nonCurrent.map((account: AccountBalance) => (
                        <div key={account.account._id} className="flex justify-between py-1">
                          <span className="ml-4">{account.account.name}</span>
                          <span>{formatCurrency(account.balance)}</span>
                        </div>
                      ))}
                      <div className="flex justify-between py-1 font-medium border-t">
                        <span className="ml-4">Total Non-Current Liabilities</span>
                        <span>{formatCurrency(balanceSheet.balanceSheet.totals.totalNonCurrentLiabilities)}</span>
                      </div>
                    </div>

                    <div className="flex justify-between py-1 font-medium border-t">
                      <span>Total Liabilities</span>
                      <span>{formatCurrency(balanceSheet.balanceSheet.totals.totalLiabilities)}</span>
                    </div>

                    {/* Equity */}
                    <div className="ml-4 mt-4">
                      <h4 className="font-medium mb-2">Equity</h4>
                      {balanceSheet.balanceSheet.equity.accounts.map((account: AccountBalance) => (
                        <div key={account.account._id} className="flex justify-between py-1">
                          <span className="ml-4">{account.account.name}</span>
                          <span>{formatCurrency(account.balance)}</span>
                        </div>
                      ))}
                      {balanceSheet.balanceSheet.equity.retainedEarnings !== 0 && (
                        <div className="flex justify-between py-1">
                          <span className="ml-4">Retained Earnings</span>
                          <span>{formatCurrency(balanceSheet.balanceSheet.equity.retainedEarnings)}</span>
                        </div>
                      )}
                      <div className="flex justify-between py-1 font-medium border-t">
                        <span className="ml-4">Total Equity</span>
                        <span>{formatCurrency(balanceSheet.balanceSheet.totals.equity)}</span>
                      </div>
                    </div>

                    <div className="flex justify-between py-2 font-bold text-lg border-t-2 border-black">
                      <span>TOTAL LIABILITIES AND EQUITY</span>
                      <span>{formatCurrency(balanceSheet.balanceSheet.totals.liabilitiesAndEquity)}</span>
                    </div>
                  </div>

                  {/* Financial Ratios */}
                  <div className="mt-6 p-4 bg-gray-50 rounded">
                    <h4 className="font-medium mb-2">Key Financial Ratios</h4>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Current Ratio:</span>
                        <p>{balanceSheet.ratios.currentRatio.toFixed(2)}</p>
                      </div>
                      <div>
                        <span className="font-medium">Working Capital:</span>
                        <p>{formatCurrency(balanceSheet.ratios.workingCapital)}</p>
                      </div>
                      <div>
                        <span className="font-medium">Debt-to-Equity:</span>
                        <p>{balanceSheet.ratios.debtToEquityRatio.toFixed(2)}</p>
                      </div>
                    </div>
                  </div>

                  {/* Validation Status */}
                  <div className="mt-4 p-4 bg-green-50 rounded">
                    <h4 className="font-medium mb-2">Validation Status</h4>
                    <p className={`text-sm ${balanceSheet.summary.isBalanced ? 'text-green-600' : 'text-red-600'}`}>
                      {balanceSheet.summary.isBalanced ? '✅ Balance Sheet is balanced' : '❌ Balance Sheet is out of balance'}
                    </p>
                    {!balanceSheet.summary.isBalanced && (
                      <p className="text-sm text-red-600">
                        Difference: {formatCurrency(balanceSheet.summary.difference)}
                      </p>
                    )}
                  </div>
                </div>
              )}

              {/* Chart of Accounts Display */}
              {selectedReport === "chart_of_accounts" && chartOfAccounts && (
                <div className="space-y-4">
                  {(["asset", "liability", "equity", "revenue", "expense"] as const).map((type: AccountType) => {
                    const accounts = chartOfAccounts.filter((account: Account) => account.type === type);
                    if (accounts.length === 0) return null;

                    return (
                      <div key={type}>
                        <h3 className="font-semibold text-lg mb-2 capitalize">
                          {type === "expense" ? "Expenses" : type + "s"}
                        </h3>
                        <div className="ml-4 space-y-1">
                          {accounts.map((account: Account) => (
                            <div key={account._id} className="flex justify-between items-center py-1 border-b border-gray-100">
                              <div>
                                <span className="font-medium">{account.code}</span>
                                <span className="ml-2">{account.name}</span>
                                {account.subtype && (
                                  <span className="ml-2 text-sm text-gray-500">({account.subtype.replace('_', ' ')})</span>
                                )}
                              </div>
                              <span className={`text-sm px-2 py-1 rounded ${
                                account.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}>
                                {account.isActive ? 'Active' : 'Inactive'}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}

              {/* Loading states */}
              {selectedReport === "balance_sheet" && !balanceSheet && (
                <div className="text-center py-8">Loading balance sheet...</div>
              )}
              {selectedReport === "income_statement" && !incomeStatement && (
                <div className="text-center py-8">Loading income statement...</div>
              )}
              {selectedReport === "trial_balance" && !trialBalance && (
                <div className="text-center py-8">Loading trial balance...</div>
              )}
              {selectedReport === "chart_of_accounts" && !chartOfAccounts && (
                <div className="text-center py-8">Loading chart of accounts...</div>
              )}

              {/* Manufacturing Cost Analysis */}
              {selectedReport === "manufacturing_costs" && (
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-xl font-semibold mb-4 flex items-center">
                    <Factory className="h-6 w-6 text-blue-600 mr-2" />
                    Manufacturing Cost Analysis
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h4 className="font-medium text-blue-900 mb-2">Direct Materials</h4>
                      <p className="text-2xl font-bold text-blue-700">₱850,000</p>
                      <p className="text-sm text-blue-600">Raw materials consumed</p>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <h4 className="font-medium text-green-900 mb-2">Direct Labor</h4>
                      <p className="text-2xl font-bold text-green-700">₱420,000</p>
                      <p className="text-sm text-green-600">Production wages</p>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <h4 className="font-medium text-purple-900 mb-2">Manufacturing Overhead</h4>
                      <p className="text-2xl font-bold text-purple-700">₱310,000</p>
                      <p className="text-sm text-purple-600">Indirect costs</p>
                    </div>
                  </div>

                  <div className="mt-6 bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">Cost Breakdown by Product Line</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Product Line A</span>
                        <span className="font-medium">₱680,000 (42%)</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Product Line B</span>
                        <span className="font-medium">₱520,000 (32%)</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Product Line C</span>
                        <span className="font-medium">₱420,000 (26%)</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
