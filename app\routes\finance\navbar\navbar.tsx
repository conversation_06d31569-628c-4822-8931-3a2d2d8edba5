import { User, <PERSON>, <PERSON>, Calendar } from "lucide-react";

export default function Navbar() {
  return (
    <nav className="bg-white shadow px-8 py-4 flex items-center justify-between border-b border-gray-200">
      <div className="flex items-center space-x-4">
        <div className="text-xl font-bold text-gray-800">XYZ Manufacturing ERP</div>
        <div className="hidden md:flex items-center space-x-2 text-sm text-gray-500">
          <Calendar size={16} />
          <span>Fiscal Year 2024</span>
        </div>
      </div>

      <div className="flex items-center space-x-6">
        {/* Search */}
        <div className="hidden md:flex items-center space-x-2">
          <Search size={18} className="text-gray-400" />
          <input
            type="text"
            placeholder="Search transactions..."
            className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Notifications */}
        <button
          type="button"
          title="Notifications"
          className="relative p-2 text-gray-600 hover:text-gray-800"
        >
          <Bell size={20} />
          <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
        </button>

        {/* User Info */}
        <div className="flex items-center space-x-3">
          <div className="text-right hidden sm:block">
            <p className="text-sm font-medium text-gray-800">Admin User</p>
            <p className="text-xs text-gray-500">Financial Controller</p>
          </div>
          <div className="h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center">
            <User size={16} className="text-white" />
          </div>
        </div>
      </div>
    </nav>
  );
}
