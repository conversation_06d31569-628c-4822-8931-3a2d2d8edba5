import { User } from "lucide-react";

export default function Navbar() {

  return (
    <nav className="bg-white shadow px-8 py-5 flex items-center justify-between border-b border-gray-200">
      <div className="text-xl font-bold text-gray-800">XYZ Manufacturing ERP</div>

      <div className="flex items-center space-x-4">
        {/* User Info */}
        <div className="flex items-center space-x-2 text-gray-600">
          <User size={20} />
          <span className="text-sm">Admin User</span>
        </div>

        {/* Logout Button */}
       
      </div>
    </nav>
  );
}
