import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { validateAccountCode } from "./utils/accounting.js";

// Create a new account
export const createAccount = mutation({
  args: {
    code: v.string(),
    name: v.string(),
    type: v.union(
      v.literal("asset"),
      v.literal("liability"),
      v.literal("equity"),
      v.literal("revenue"),
      v.literal("expense")
    ),
    subtype: v.optional(v.union(
      v.literal("current_asset"),
      v.literal("non_current_asset"),
      v.literal("current_liability"),
      v.literal("non_current_liability"),
      v.literal("operating_revenue"),
      v.literal("other_revenue"),
      v.literal("cost_of_sales"),
      v.literal("operating_expense"),
      v.literal("administrative_expense"),
      v.literal("finance_cost"),
      v.literal("other_expense")
    )),
    parentAccountId: v.optional(v.id("accounts")),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Validate account code format
    if (!validateAccountCode(args.code)) {
      throw new Error("Account code must be a 4-digit number (e.g., 1001)");
    }

    // Check if account code already exists
    const existingAccount = await ctx.db
      .query("accounts")
      .withIndex("by_code", (q) => q.eq("code", args.code))
      .first();

    if (existingAccount) {
      throw new Error(`Account code ${args.code} already exists`);
    }

    // Validate parent account if provided
    if (args.parentAccountId) {
      const parentAccount = await ctx.db.get(args.parentAccountId);
      if (!parentAccount) {
        throw new Error("Parent account not found");
      }
      
      // Ensure parent account is of the same type
      if (parentAccount.type !== args.type) {
        throw new Error("Parent account must be of the same type");
      }
    }

    // Validate account type and subtype combination
    const validSubtypes = {
      asset: ["current_asset", "non_current_asset"],
      liability: ["current_liability", "non_current_liability"],
      equity: [],
      revenue: ["operating_revenue", "other_revenue"],
      expense: ["cost_of_sales", "operating_expense", "administrative_expense", "finance_cost", "other_expense"]
    };

    if (args.subtype && !validSubtypes[args.type].includes(args.subtype)) {
      throw new Error(`Invalid subtype ${args.subtype} for account type ${args.type}`);
    }

    const now = Date.now();
    
    return await ctx.db.insert("accounts", {
      code: args.code,
      name: args.name,
      type: args.type,
      subtype: args.subtype,
      parentAccountId: args.parentAccountId,
      isActive: true,
      description: args.description,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Get chart of accounts as nested tree
export const getChartOfAccounts = query({
  args: {
    includeInactive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const includeInactive = args.includeInactive || false;
    
    // Get all accounts
    let accounts = await ctx.db.query("accounts").collect();
    
    if (!includeInactive) {
      accounts = accounts.filter(account => account.isActive);
    }

    // Sort accounts by type and code
    const typeOrder = ["asset", "liability", "equity", "revenue", "expense"];
    accounts.sort((a, b) => {
      const typeComparison = typeOrder.indexOf(a.type) - typeOrder.indexOf(b.type);
      if (typeComparison !== 0) return typeComparison;
      return a.code.localeCompare(b.code);
    });

    // Build nested tree structure
    const accountMap = new Map();
    const rootAccounts = [];

    // First pass: create map of all accounts
    accounts.forEach(account => {
      accountMap.set(account._id, { ...account, children: [] });
    });

    // Second pass: build tree structure
    accounts.forEach(account => {
      const accountNode = accountMap.get(account._id);
      
      if (account.parentAccountId) {
        const parent = accountMap.get(account.parentAccountId);
        if (parent) {
          parent.children.push(accountNode);
        } else {
          // Parent not found or inactive, treat as root
          rootAccounts.push(accountNode);
        }
      } else {
        rootAccounts.push(accountNode);
      }
    });

    return rootAccounts;
  },
});

// Get accounts by type
export const getAccountsByType = query({
  args: {
    type: v.union(
      v.literal("asset"),
      v.literal("liability"),
      v.literal("equity"),
      v.literal("revenue"),
      v.literal("expense")
    ),
    includeInactive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const includeInactive = args.includeInactive || false;
    
    let accounts = await ctx.db
      .query("accounts")
      .withIndex("by_type", (q) => q.eq("type", args.type))
      .collect();

    if (!includeInactive) {
      accounts = accounts.filter(account => account.isActive);
    }

    return accounts.sort((a, b) => a.code.localeCompare(b.code));
  },
});

// Update account
export const updateAccount = mutation({
  args: {
    accountId: v.id("accounts"),
    name: v.optional(v.string()),
    parentAccountId: v.optional(v.id("accounts")),
    description: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const account = await ctx.db.get(args.accountId);
    if (!account) {
      throw new Error("Account not found");
    }

    // Validate parent account if being changed
    if (args.parentAccountId && args.parentAccountId !== account.parentAccountId) {
      const parentAccount = await ctx.db.get(args.parentAccountId);
      if (!parentAccount) {
        throw new Error("Parent account not found");
      }
      
      // Ensure parent account is of the same type
      if (parentAccount.type !== account.type) {
        throw new Error("Parent account must be of the same type");
      }

      // Prevent circular references
      if (args.parentAccountId === args.accountId) {
        throw new Error("Account cannot be its own parent");
      }

      // Check if the new parent would create a circular reference
      let currentParent = parentAccount;
      while (currentParent.parentAccountId) {
        if (currentParent.parentAccountId === args.accountId) {
          throw new Error("This would create a circular reference");
        }
        currentParent = await ctx.db.get(currentParent.parentAccountId);
        if (!currentParent) break;
      }
    }

    const updateData = {
      updatedAt: Date.now(),
    };

    if (args.name !== undefined) updateData.name = args.name;
    if (args.parentAccountId !== undefined) updateData.parentAccountId = args.parentAccountId;
    if (args.description !== undefined) updateData.description = args.description;
    if (args.isActive !== undefined) updateData.isActive = args.isActive;

    await ctx.db.patch(args.accountId, updateData);
    
    return await ctx.db.get(args.accountId);
  },
});

// Delete account (only if no journal entries exist)
export const deleteAccount = mutation({
  args: {
    accountId: v.id("accounts"),
  },
  handler: async (ctx, args) => {
    const account = await ctx.db.get(args.accountId);
    if (!account) {
      throw new Error("Account not found");
    }

    // Check if account has any journal entry lines
    const journalEntryLines = await ctx.db
      .query("journal_entry_lines")
      .withIndex("by_account", (q) => q.eq("accountId", args.accountId))
      .first();

    if (journalEntryLines) {
      throw new Error("Cannot delete account with existing journal entries. Consider deactivating instead.");
    }

    // Check if account has child accounts
    const childAccounts = await ctx.db
      .query("accounts")
      .withIndex("by_parent", (q) => q.eq("parentAccountId", args.accountId))
      .first();

    if (childAccounts) {
      throw new Error("Cannot delete account with child accounts. Delete or reassign child accounts first.");
    }

    await ctx.db.delete(args.accountId);
    return { success: true };
  },
});

// Get account by code
export const getAccountByCode = query({
  args: {
    code: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("accounts")
      .withIndex("by_code", (q) => q.eq("code", args.code))
      .first();
  },
});

// Get account details with balance information
export const getAccountDetails = query({
  args: {
    accountId: v.id("accounts"),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const account = await ctx.db.get(args.accountId);
    if (!account) {
      throw new Error("Account not found");
    }

    // Get journal entry lines for this account within date range
    let journalLines = await ctx.db
      .query("journal_entry_lines")
      .withIndex("by_account", (q) => q.eq("accountId", args.accountId))
      .collect();

    if (args.startDate || args.endDate) {
      // Filter by date range if provided
      const journalEntryIds = journalLines.map(line => line.journalEntryId);
      const journalEntries = await Promise.all(
        journalEntryIds.map(id => ctx.db.get(id))
      );

      const filteredEntryIds = journalEntries
        .filter(entry => {
          if (!entry) return false;
          if (args.startDate && entry.date < args.startDate) return false;
          if (args.endDate && entry.date > args.endDate) return false;
          return entry.status === "posted";
        })
        .map(entry => entry._id);

      journalLines = journalLines.filter(line => 
        filteredEntryIds.includes(line.journalEntryId)
      );
    }

    // Calculate totals
    const totalDebits = journalLines.reduce((sum, line) => sum + line.debit, 0);
    const totalCredits = journalLines.reduce((sum, line) => sum + line.credit, 0);

    return {
      ...account,
      totalDebits,
      totalCredits,
      balance: totalDebits - totalCredits,
      transactionCount: journalLines.length,
    };
  },
});
