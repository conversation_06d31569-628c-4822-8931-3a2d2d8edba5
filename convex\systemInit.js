import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Initialize the accounting system with Philippine GAAP/PFRS compliant chart of accounts
export const initializeAccountingSystem = mutation({
  args: {
    companyName: v.string(),
    fiscalYearEnd: v.optional(v.string()), // "12-31" format
    baseCurrency: v.optional(v.string()),
    createdBy: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if system is already initialized
    const existingAccounts = await ctx.db.query("accounts").first();
    if (existingAccounts) {
      throw new Error("Accounting system is already initialized. Use individual account creation functions to add more accounts.");
    }

    const now = Date.now();
    const fiscalYearEnd = args.fiscalYearEnd || "12-31";
    const baseCurrency = args.baseCurrency || "PHP";

    // Philippine GAAP/PFRS compliant Chart of Accounts
    const defaultAccounts = [
      // ASSETS (1000-2999)
      // Current Assets (1000-1999)
      { code: "1001", name: "Cash on Hand", type: "asset", subtype: "current_asset", description: "Petty cash and cash on hand" },
      { code: "1002", name: "Cash in Bank - Current Account", type: "asset", subtype: "current_asset", description: "Checking account balance" },
      { code: "1003", name: "Cash in Bank - Savings Account", type: "asset", subtype: "current_asset", description: "Savings account balance" },
      { code: "1101", name: "Accounts Receivable - Trade", type: "asset", subtype: "current_asset", description: "Trade receivables from customers" },
      { code: "1102", name: "Allowance for Doubtful Accounts", type: "asset", subtype: "current_asset", description: "Contra asset for bad debts" },
      { code: "1201", name: "Inventory - Raw Materials", type: "asset", subtype: "current_asset", description: "Raw materials inventory" },
      { code: "1202", name: "Inventory - Work in Process", type: "asset", subtype: "current_asset", description: "Work in process inventory" },
      { code: "1203", name: "Inventory - Finished Goods", type: "asset", subtype: "current_asset", description: "Finished goods inventory" },
      { code: "1301", name: "Prepaid Expenses", type: "asset", subtype: "current_asset", description: "Prepaid expenses and deposits" },
      { code: "1302", name: "Prepaid Insurance", type: "asset", subtype: "current_asset", description: "Prepaid insurance premiums" },
      { code: "1401", name: "Other Current Assets", type: "asset", subtype: "current_asset", description: "Other current assets" },

      // Non-Current Assets (2000-2999)
      { code: "2001", name: "Land", type: "asset", subtype: "non_current_asset", description: "Land and real estate" },
      { code: "2002", name: "Buildings", type: "asset", subtype: "non_current_asset", description: "Buildings and structures" },
      { code: "2003", name: "Accumulated Depreciation - Buildings", type: "asset", subtype: "non_current_asset", description: "Contra asset for building depreciation" },
      { code: "2101", name: "Machinery and Equipment", type: "asset", subtype: "non_current_asset", description: "Machinery and equipment" },
      { code: "2102", name: "Accumulated Depreciation - Machinery", type: "asset", subtype: "non_current_asset", description: "Contra asset for machinery depreciation" },
      { code: "2201", name: "Furniture and Fixtures", type: "asset", subtype: "non_current_asset", description: "Furniture and fixtures" },
      { code: "2202", name: "Accumulated Depreciation - Furniture", type: "asset", subtype: "non_current_asset", description: "Contra asset for furniture depreciation" },
      { code: "2301", name: "Computer Equipment", type: "asset", subtype: "non_current_asset", description: "Computer hardware and equipment" },
      { code: "2302", name: "Accumulated Depreciation - Computer", type: "asset", subtype: "non_current_asset", description: "Contra asset for computer depreciation" },
      { code: "2401", name: "Intangible Assets", type: "asset", subtype: "non_current_asset", description: "Patents, trademarks, software licenses" },
      { code: "2501", name: "Investment in Subsidiaries", type: "asset", subtype: "non_current_asset", description: "Long-term investments" },

      // LIABILITIES (3000-4999)
      // Current Liabilities (3000-3999)
      { code: "3001", name: "Accounts Payable - Trade", type: "liability", subtype: "current_liability", description: "Trade payables to suppliers" },
      { code: "3002", name: "Accounts Payable - Others", type: "liability", subtype: "current_liability", description: "Other payables" },
      { code: "3101", name: "Accrued Expenses", type: "liability", subtype: "current_liability", description: "Accrued expenses payable" },
      { code: "3102", name: "Accrued Salaries and Wages", type: "liability", subtype: "current_liability", description: "Accrued employee compensation" },
      { code: "3201", name: "Notes Payable - Current", type: "liability", subtype: "current_liability", description: "Short-term notes payable" },
      { code: "3301", name: "Income Tax Payable", type: "liability", subtype: "current_liability", description: "Income tax liability" },
      { code: "3302", name: "VAT Payable", type: "liability", subtype: "current_liability", description: "Value-added tax payable" },
      { code: "3303", name: "Withholding Tax Payable", type: "liability", subtype: "current_liability", description: "Withholding tax payable" },
      { code: "3401", name: "SSS Payable", type: "liability", subtype: "current_liability", description: "Social Security System contributions" },
      { code: "3402", name: "PhilHealth Payable", type: "liability", subtype: "current_liability", description: "PhilHealth contributions" },
      { code: "3403", name: "Pag-IBIG Payable", type: "liability", subtype: "current_liability", description: "Pag-IBIG contributions" },

      // Non-Current Liabilities (4000-4999)
      { code: "4001", name: "Notes Payable - Long Term", type: "liability", subtype: "non_current_liability", description: "Long-term notes payable" },
      { code: "4002", name: "Loans Payable - Long Term", type: "liability", subtype: "non_current_liability", description: "Long-term loans payable" },
      { code: "4101", name: "Deferred Tax Liability", type: "liability", subtype: "non_current_liability", description: "Deferred tax liability" },

      // EQUITY (5000-5999)
      { code: "5001", name: "Capital Stock", type: "equity", description: "Issued capital stock" },
      { code: "5002", name: "Additional Paid-in Capital", type: "equity", description: "Premium on capital stock" },
      { code: "5101", name: "Retained Earnings", type: "equity", description: "Accumulated retained earnings" },
      { code: "5102", name: "Current Year Earnings", type: "equity", description: "Current year net income" },

      // REVENUE (6000-6999)
      { code: "6001", name: "Sales Revenue", type: "revenue", subtype: "operating_revenue", description: "Revenue from sales of goods/services" },
      { code: "6002", name: "Service Revenue", type: "revenue", subtype: "operating_revenue", description: "Revenue from services" },
      { code: "6101", name: "Interest Income", type: "revenue", subtype: "other_revenue", description: "Interest earned on investments" },
      { code: "6102", name: "Dividend Income", type: "revenue", subtype: "other_revenue", description: "Dividends received" },
      { code: "6201", name: "Other Income", type: "revenue", subtype: "other_revenue", description: "Miscellaneous income" },

      // EXPENSES (7000-9999)
      // Cost of Sales (7000-7999)
      { code: "7001", name: "Cost of Goods Sold", type: "expense", subtype: "cost_of_sales", description: "Direct cost of goods sold" },
      { code: "7002", name: "Cost of Services", type: "expense", subtype: "cost_of_sales", description: "Direct cost of services provided" },

      // Operating Expenses (8000-8999)
      { code: "8001", name: "Salaries and Wages", type: "expense", subtype: "operating_expense", description: "Employee salaries and wages" },
      { code: "8002", name: "Employee Benefits", type: "expense", subtype: "operating_expense", description: "Employee benefits and bonuses" },
      { code: "8101", name: "Rent Expense", type: "expense", subtype: "operating_expense", description: "Office and facility rent" },
      { code: "8102", name: "Utilities Expense", type: "expense", subtype: "operating_expense", description: "Electricity, water, telecommunications" },
      { code: "8201", name: "Depreciation Expense", type: "expense", subtype: "operating_expense", description: "Depreciation of fixed assets" },
      { code: "8202", name: "Amortization Expense", type: "expense", subtype: "operating_expense", description: "Amortization of intangible assets" },
      { code: "8301", name: "Professional Fees", type: "expense", subtype: "administrative_expense", description: "Legal, accounting, consulting fees" },
      { code: "8302", name: "Office Supplies", type: "expense", subtype: "administrative_expense", description: "Office supplies and materials" },
      { code: "8401", name: "Marketing and Advertising", type: "expense", subtype: "operating_expense", description: "Marketing and promotional expenses" },
      { code: "8501", name: "Travel and Transportation", type: "expense", subtype: "administrative_expense", description: "Business travel expenses" },

      // Finance Costs (9000-9999)
      { code: "9001", name: "Interest Expense", type: "expense", subtype: "finance_cost", description: "Interest on loans and borrowings" },
      { code: "9002", name: "Bank Charges", type: "expense", subtype: "finance_cost", description: "Bank fees and charges" },
      { code: "9101", name: "Other Expenses", type: "expense", subtype: "other_expense", description: "Miscellaneous expenses" },
    ];

    // Create all accounts
    const accountPromises = defaultAccounts.map(account => 
      ctx.db.insert("accounts", {
        ...account,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      })
    );

    const createdAccounts = await Promise.all(accountPromises);

    // Initialize system settings
    const settingsPromises = [
      ctx.db.insert("accounting_settings", {
        key: "company_name",
        value: args.companyName,
        description: "Company name for financial reports",
        updatedAt: now,
      }),
      ctx.db.insert("accounting_settings", {
        key: "fiscal_year_end",
        value: fiscalYearEnd,
        description: "Fiscal year end date (MM-DD format)",
        updatedAt: now,
      }),
      ctx.db.insert("accounting_settings", {
        key: "base_currency",
        value: baseCurrency,
        description: "Base currency for financial reporting",
        updatedAt: now,
      }),
      ctx.db.insert("accounting_settings", {
        key: "system_initialized",
        value: true,
        description: "System initialization status",
        updatedAt: now,
      }),
      ctx.db.insert("accounting_settings", {
        key: "pfrs_compliance",
        value: true,
        description: "Philippine Financial Reporting Standards compliance enabled",
        updatedAt: now,
      }),
      ctx.db.insert("accounting_settings", {
        key: "bir_compliance",
        value: true,
        description: "Bureau of Internal Revenue compliance enabled",
        updatedAt: now,
      }),
    ];

    await Promise.all(settingsPromises);

    return {
      success: true,
      message: "Accounting system initialized successfully with Philippine GAAP/PFRS compliant chart of accounts",
      summary: {
        companyName: args.companyName,
        accountsCreated: createdAccounts.length,
        fiscalYearEnd,
        baseCurrency,
        pfrsCompliant: true,
        birCompliant: true,
        initializedAt: now,
        initializedBy: args.createdBy,
      },
      accounts: {
        assets: defaultAccounts.filter(a => a.type === 'asset').length,
        liabilities: defaultAccounts.filter(a => a.type === 'liability').length,
        equity: defaultAccounts.filter(a => a.type === 'equity').length,
        revenue: defaultAccounts.filter(a => a.type === 'revenue').length,
        expenses: defaultAccounts.filter(a => a.type === 'expense').length,
      },
    };
  },
});

// Get system initialization status
export const getSystemStatus = query({
  args: {},
  handler: async (ctx, args) => {
    const settings = await ctx.db.query("accounting_settings").collect();
    const accounts = await ctx.db.query("accounts").collect();
    const journalEntries = await ctx.db.query("journal_entries").collect();

    const settingsMap = {};
    settings.forEach(setting => {
      settingsMap[setting.key] = setting.value;
    });

    const isInitialized = settingsMap.system_initialized || false;

    return {
      isInitialized,
      companyName: settingsMap.company_name || null,
      fiscalYearEnd: settingsMap.fiscal_year_end || "12-31",
      baseCurrency: settingsMap.base_currency || "PHP",
      pfrsCompliance: settingsMap.pfrs_compliance || false,
      birCompliance: settingsMap.bir_compliance || false,
      statistics: {
        totalAccounts: accounts.length,
        activeAccounts: accounts.filter(a => a.isActive).length,
        totalJournalEntries: journalEntries.length,
        postedEntries: journalEntries.filter(e => e.status === "posted").length,
        accountsByType: {
          assets: accounts.filter(a => a.type === 'asset').length,
          liabilities: accounts.filter(a => a.type === 'liability').length,
          equity: accounts.filter(a => a.type === 'equity').length,
          revenue: accounts.filter(a => a.type === 'revenue').length,
          expenses: accounts.filter(a => a.type === 'expense').length,
        },
      },
      lastUpdated: Math.max(...settings.map(s => s.updatedAt), 0),
    };
  },
});
