{"name": "xyz-finance", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit", "convex:dev": "convex dev", "convex:deploy": "convex deploy"}, "dependencies": {"@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "convex": "^1.24.8", "isbot": "^5.1.27", "lucide-react": "^0.515.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.5.3"}, "devDependencies": {"@eslint/js": "^9.17.0", "@react-router/dev": "^7.5.3", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "autoprefixer": "^10.4.21", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0", "postcss": "^8.5.5", "tailwindcss": "^4.0.0", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}