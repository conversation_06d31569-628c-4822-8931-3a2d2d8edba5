import { query } from "./_generated/server";
import { v } from "convex/values";
import { calculateAccountBalance, getAccountClassification } from "./utils/accounting.js";

// Generate trial balance
export const generateTrialBalance = query({
  args: {
    startDate: v.optional(v.number()),
    endDate: v.number(),
    includeZeroBalances: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const includeZeroBalances = args.includeZeroBalances || false;

    // Get all active accounts
    const accounts = await ctx.db
      .query("accounts")
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    // Get all posted journal entries within date range
    let journalEntries = await ctx.db
      .query("journal_entries")
      .withIndex("by_status", (q) => q.eq("status", "posted"))
      .collect();

    // Filter by date range
    journalEntries = journalEntries.filter(entry => {
      if (args.startDate && entry.date < args.startDate) return false;
      if (entry.date > args.endDate) return false;
      return true;
    });

    const journalEntryIds = journalEntries.map(entry => entry._id);

    // Get all journal entry lines for the filtered entries
    const allJournalLines = await ctx.db
      .query("journal_entry_lines")
      .collect();

    const relevantLines = allJournalLines.filter(line =>
      journalEntryIds.includes(line.journalEntryId)
    );

    // Group lines by account
    const accountBalances = new Map();

    // Initialize all accounts with zero balances
    accounts.forEach(account => {
      accountBalances.set(account._id, {
        account,
        totalDebits: 0,
        totalCredits: 0,
        balance: 0,
        classification: getAccountClassification(account.type, account.subtype),
      });
    });

    // Calculate totals for each account
    relevantLines.forEach(line => {
      const accountData = accountBalances.get(line.accountId);
      if (accountData) {
        accountData.totalDebits += line.debit;
        accountData.totalCredits += line.credit;
      }
    });

    // Calculate final balances
    accountBalances.forEach((data, accountId) => {
      data.balance = calculateAccountBalance(
        data.account.type,
        0, // No opening balance for trial balance
        data.totalDebits,
        data.totalCredits
      );
    });

    // Convert to array and filter zero balances if requested
    let trialBalanceData = Array.from(accountBalances.values());

    if (!includeZeroBalances) {
      trialBalanceData = trialBalanceData.filter(data => 
        Math.abs(data.balance) > 0.01 // Allow for rounding differences
      );
    }

    // Sort by account code
    trialBalanceData.sort((a, b) => a.account.code.localeCompare(b.account.code));

    // Group by account type for better presentation
    const groupedData = {
      assets: trialBalanceData.filter(data => data.account.type === 'asset'),
      liabilities: trialBalanceData.filter(data => data.account.type === 'liability'),
      equity: trialBalanceData.filter(data => data.account.type === 'equity'),
      revenue: trialBalanceData.filter(data => data.account.type === 'revenue'),
      expenses: trialBalanceData.filter(data => data.account.type === 'expense'),
    };

    // Calculate totals
    const totalDebits = trialBalanceData.reduce((sum, data) => sum + data.totalDebits, 0);
    const totalCredits = trialBalanceData.reduce((sum, data) => sum + data.totalCredits, 0);

    // Calculate debit and credit balances for trial balance presentation
    let totalDebitBalances = 0;
    let totalCreditBalances = 0;

    trialBalanceData.forEach(data => {
      if (data.balance > 0) {
        // Positive balance
        if (data.account.type === 'asset' || data.account.type === 'expense') {
          totalDebitBalances += data.balance;
        } else {
          totalCreditBalances += data.balance;
        }
      } else if (data.balance < 0) {
        // Negative balance (unusual but possible)
        if (data.account.type === 'asset' || data.account.type === 'expense') {
          totalCreditBalances += Math.abs(data.balance);
        } else {
          totalDebitBalances += Math.abs(data.balance);
        }
      }
    });

    // Validation - trial balance should balance
    const isBalanced = Math.abs(totalDebitBalances - totalCreditBalances) < 0.01;
    const difference = totalDebitBalances - totalCreditBalances;

    return {
      trialBalance: trialBalanceData,
      groupedData,
      summary: {
        totalAccounts: trialBalanceData.length,
        totalDebits,
        totalCredits,
        totalDebitBalances,
        totalCreditBalances,
        isBalanced,
        difference,
        dateRange: {
          startDate: args.startDate,
          endDate: args.endDate,
        },
        generatedAt: Date.now(),
      },
      validation: {
        isValid: isBalanced,
        errors: isBalanced ? [] : [`Trial balance is out of balance by ${difference.toFixed(2)}`],
        warnings: [],
      },
    };
  },
});

// Generate adjusted trial balance (includes adjusting entries)
export const generateAdjustedTrialBalance = query({
  args: {
    startDate: v.optional(v.number()),
    endDate: v.number(),
    includeZeroBalances: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Get regular trial balance
    const regularTrialBalance = await generateTrialBalance(ctx, args);

    // Get adjusting entries within the period
    let adjustingEntries = await ctx.db
      .query("journal_entries")
      .withIndex("by_status", (q) => q.eq("status", "posted"))
      .collect();

    adjustingEntries = adjustingEntries.filter(entry => {
      if (entry.entryType !== "adjusting") return false;
      if (args.startDate && entry.date < args.startDate) return false;
      if (entry.date > args.endDate) return false;
      return true;
    });

    const adjustingEntryIds = adjustingEntries.map(entry => entry._id);

    // Get adjusting entry lines
    const allJournalLines = await ctx.db
      .query("journal_entry_lines")
      .collect();

    const adjustingLines = allJournalLines.filter(line =>
      adjustingEntryIds.includes(line.journalEntryId)
    );

    // Apply adjustments to trial balance
    const adjustedData = regularTrialBalance.trialBalance.map(data => {
      const accountAdjustments = adjustingLines.filter(line => 
        line.accountId === data.account._id
      );

      let adjustmentDebits = 0;
      let adjustmentCredits = 0;

      accountAdjustments.forEach(line => {
        adjustmentDebits += line.debit;
        adjustmentCredits += line.credit;
      });

      const adjustedTotalDebits = data.totalDebits + adjustmentDebits;
      const adjustedTotalCredits = data.totalCredits + adjustmentCredits;
      const adjustedBalance = calculateAccountBalance(
        data.account.type,
        0,
        adjustedTotalDebits,
        adjustedTotalCredits
      );

      return {
        ...data,
        adjustmentDebits,
        adjustmentCredits,
        adjustedTotalDebits,
        adjustedTotalCredits,
        adjustedBalance,
        netAdjustment: adjustmentDebits - adjustmentCredits,
      };
    });

    // Recalculate totals
    const adjustedTotalDebits = adjustedData.reduce((sum, data) => sum + data.adjustedTotalDebits, 0);
    const adjustedTotalCredits = adjustedData.reduce((sum, data) => sum + data.adjustedTotalCredits, 0);

    let adjustedDebitBalances = 0;
    let adjustedCreditBalances = 0;

    adjustedData.forEach(data => {
      if (data.adjustedBalance > 0) {
        if (data.account.type === 'asset' || data.account.type === 'expense') {
          adjustedDebitBalances += data.adjustedBalance;
        } else {
          adjustedCreditBalances += data.adjustedBalance;
        }
      } else if (data.adjustedBalance < 0) {
        if (data.account.type === 'asset' || data.account.type === 'expense') {
          adjustedCreditBalances += Math.abs(data.adjustedBalance);
        } else {
          adjustedDebitBalances += Math.abs(data.adjustedBalance);
        }
      }
    });

    const isAdjustedBalanced = Math.abs(adjustedDebitBalances - adjustedCreditBalances) < 0.01;
    const adjustedDifference = adjustedDebitBalances - adjustedCreditBalances;

    // Group adjusted data
    const adjustedGroupedData = {
      assets: adjustedData.filter(data => data.account.type === 'asset'),
      liabilities: adjustedData.filter(data => data.account.type === 'liability'),
      equity: adjustedData.filter(data => data.account.type === 'equity'),
      revenue: adjustedData.filter(data => data.account.type === 'revenue'),
      expenses: adjustedData.filter(data => data.account.type === 'expense'),
    };

    return {
      adjustedTrialBalance: adjustedData,
      groupedData: adjustedGroupedData,
      originalTrialBalance: regularTrialBalance,
      adjustingEntries: adjustingEntries.length,
      summary: {
        totalAccounts: adjustedData.length,
        adjustedTotalDebits,
        adjustedTotalCredits,
        adjustedDebitBalances,
        adjustedCreditBalances,
        isBalanced: isAdjustedBalanced,
        difference: adjustedDifference,
        dateRange: {
          startDate: args.startDate,
          endDate: args.endDate,
        },
        generatedAt: Date.now(),
      },
      validation: {
        isValid: isAdjustedBalanced,
        errors: isAdjustedBalanced ? [] : [`Adjusted trial balance is out of balance by ${adjustedDifference.toFixed(2)}`],
        warnings: [],
      },
    };
  },
});
