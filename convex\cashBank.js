import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Record a cash transaction
export const recordCashTransaction = mutation({
  args: {
    type: v.union(v.literal("inflow"), v.literal("outflow")),
    accountId: v.id("accounts"), // Cash/Bank account
    amount: v.number(),
    description: v.string(),
    reference: v.optional(v.string()),
    date: v.optional(v.number()),
    counterAccountId: v.optional(v.id("accounts")), // The other account affected
    createdBy: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Validate amount
    if (args.amount <= 0) {
      throw new Error("Transaction amount must be greater than zero");
    }

    // Get and validate the cash account
    const cashAccount = await ctx.db.get(args.accountId);
    if (!cashAccount || !cashAccount.isActive) {
      throw new Error("Cash account not found or inactive");
    }

    if (cashAccount.type !== 'asset') {
      throw new Error("Cash account must be an asset account");
    }

    // Validate counter account if provided
    let counterAccount = null;
    if (args.counterAccountId) {
      counterAccount = await ctx.db.get(args.counterAccountId);
      if (!counterAccount || !counterAccount.isActive) {
        throw new Error("Counter account not found or inactive");
      }
    }

    const transactionDate = args.date || Date.now();

    // Determine journal entry lines based on transaction type
    let lines = [];
    
    if (args.type === "inflow") {
      // Cash inflow: Debit Cash, Credit other account
      lines = [
        {
          accountId: args.accountId,
          debit: args.amount,
          credit: 0,
          description: args.description,
        }
      ];

      if (args.counterAccountId) {
        lines.push({
          accountId: args.counterAccountId,
          debit: 0,
          credit: args.amount,
          description: args.description,
        });
      }
    } else {
      // Cash outflow: Credit Cash, Debit other account
      lines = [
        {
          accountId: args.accountId,
          debit: 0,
          credit: args.amount,
          description: args.description,
        }
      ];

      if (args.counterAccountId) {
        lines.push({
          accountId: args.counterAccountId,
          debit: args.amount,
          credit: 0,
          description: args.description,
        });
      }
    }

    // If no counter account provided, this is an incomplete entry
    if (!args.counterAccountId) {
      throw new Error("Counter account is required for complete double-entry transaction");
    }

    // Create journal entry
    const journalEntryData = {
      date: transactionDate,
      description: `${args.type === "inflow" ? "Cash Receipt" : "Cash Payment"}: ${args.description}`,
      reference: args.reference,
      entryType: "regular",
      lines,
      createdBy: args.createdBy,
    };

    // Import and call postJournalEntry
    const { postJournalEntry } = await import("./generalLedger.js");
    const journalResult = await postJournalEntry(ctx, journalEntryData);

    // Update cash account balance in cash_bank_accounts if it exists
    const cashBankAccount = await ctx.db
      .query("cash_bank_accounts")
      .withIndex("by_account", (q) => q.eq("accountId", args.accountId))
      .first();

    if (cashBankAccount) {
      const balanceChange = args.type === "inflow" ? args.amount : -args.amount;
      await ctx.db.patch(cashBankAccount._id, {
        currentBalance: cashBankAccount.currentBalance + balanceChange,
      });
    }

    return {
      journalEntryId: journalResult.journalEntryId,
      entryNumber: journalResult.entryNumber,
      transactionType: args.type,
      amount: args.amount,
      warnings: journalResult.warnings,
    };
  },
});

// Setup a cash/bank account
export const setupCashBankAccount = mutation({
  args: {
    accountId: v.id("accounts"),
    bankName: v.optional(v.string()),
    accountNumber: v.optional(v.string()),
    accountType: v.union(
      v.literal("cash"),
      v.literal("checking"),
      v.literal("savings"),
      v.literal("petty_cash")
    ),
    initialBalance: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Validate the account exists and is an asset
    const account = await ctx.db.get(args.accountId);
    if (!account || !account.isActive) {
      throw new Error("Account not found or inactive");
    }

    if (account.type !== 'asset') {
      throw new Error("Cash/Bank account must be an asset account");
    }

    // Check if cash bank account already exists
    const existingCashAccount = await ctx.db
      .query("cash_bank_accounts")
      .withIndex("by_account", (q) => q.eq("accountId", args.accountId))
      .first();

    if (existingCashAccount) {
      throw new Error("Cash/Bank account setup already exists for this account");
    }

    // Create cash bank account record
    const cashBankAccountId = await ctx.db.insert("cash_bank_accounts", {
      accountId: args.accountId,
      bankName: args.bankName,
      accountNumber: args.accountNumber,
      accountType: args.accountType,
      currentBalance: args.initialBalance || 0,
      isActive: true,
    });

    return {
      cashBankAccountId,
      message: "Cash/Bank account setup completed successfully",
    };
  },
});

// Get bank reconciliation data
export const getBankReconciliation = query({
  args: {
    bankAccountId: v.id("accounts"),
    statementDate: v.number(),
    statementBalance: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Get the cash bank account record
    const cashBankAccount = await ctx.db
      .query("cash_bank_accounts")
      .withIndex("by_account", (q) => q.eq("accountId", args.bankAccountId))
      .first();

    if (!cashBankAccount) {
      throw new Error("Cash/Bank account not found. Please setup the account first.");
    }

    // Get all journal entry lines for this account up to statement date
    let journalLines = await ctx.db
      .query("journal_entry_lines")
      .withIndex("by_account", (q) => q.eq("accountId", args.bankAccountId))
      .collect();

    // Get corresponding journal entries and filter by date
    const journalEntryIds = [...new Set(journalLines.map(line => line.journalEntryId))];
    const journalEntries = await Promise.all(
      journalEntryIds.map(id => ctx.db.get(id))
    );

    const validEntryIds = journalEntries
      .filter(entry => {
        if (!entry || entry.status !== "posted") return false;
        return entry.date <= args.statementDate;
      })
      .map(entry => entry._id);

    journalLines = journalLines.filter(line => 
      validEntryIds.includes(line.journalEntryId)
    );

    // Get detailed transaction information
    const transactions = await Promise.all(
      journalLines.map(async (line) => {
        const journalEntry = await ctx.db.get(line.journalEntryId);
        return {
          ...line,
          journalEntry,
          netAmount: line.debit - line.credit,
          isCleared: false, // This would be set based on bank statement matching
        };
      })
    );

    // Sort by date
    transactions.sort((a, b) => a.journalEntry.date - b.journalEntry.date);

    // Calculate book balance
    const bookBalance = transactions.reduce((sum, t) => sum + t.netAmount, 0);

    // Calculate outstanding items (this would be enhanced with actual clearing data)
    const outstandingDeposits = transactions.filter(t => t.netAmount > 0 && !t.isCleared);
    const outstandingChecks = transactions.filter(t => t.netAmount < 0 && !t.isCleared);

    const totalOutstandingDeposits = outstandingDeposits.reduce((sum, t) => sum + t.netAmount, 0);
    const totalOutstandingChecks = Math.abs(outstandingChecks.reduce((sum, t) => sum + t.netAmount, 0));

    // Calculate reconciled balance
    const reconciledBalance = args.statementBalance ? 
      args.statementBalance + totalOutstandingDeposits - totalOutstandingChecks : 
      bookBalance;

    const difference = bookBalance - reconciledBalance;
    const isReconciled = Math.abs(difference) < 0.01;

    return {
      reconciliation: {
        account: {
          ...cashBankAccount,
          accountDetails: await ctx.db.get(args.bankAccountId),
        },
        statementDate: args.statementDate,
        statementBalance: args.statementBalance,
        bookBalance,
        reconciledBalance,
        difference,
        isReconciled,
      },
      transactions,
      outstandingItems: {
        deposits: outstandingDeposits,
        checks: outstandingChecks,
        totalOutstandingDeposits,
        totalOutstandingChecks,
      },
      summary: {
        totalTransactions: transactions.length,
        totalDebits: transactions.reduce((sum, t) => sum + Math.max(0, t.netAmount), 0),
        totalCredits: Math.abs(transactions.reduce((sum, t) => sum + Math.min(0, t.netAmount), 0)),
        generatedAt: Date.now(),
      },
    };
  },
});

// Get cash position summary
export const getCashPositionSummary = query({
  args: {
    asOfDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const asOfDate = args.asOfDate || Date.now();

    // Get all cash bank accounts
    const cashBankAccounts = await ctx.db
      .query("cash_bank_accounts")
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    const cashPositions = await Promise.all(
      cashBankAccounts.map(async (cashAccount) => {
        // Get account details
        const accountDetails = await ctx.db.get(cashAccount.accountId);

        // Get all journal entry lines for this account up to the date
        let journalLines = await ctx.db
          .query("journal_entry_lines")
          .withIndex("by_account", (q) => q.eq("accountId", cashAccount.accountId))
          .collect();

        // Filter by date and status
        const journalEntryIds = [...new Set(journalLines.map(line => line.journalEntryId))];
        const journalEntries = await Promise.all(
          journalEntryIds.map(id => ctx.db.get(id))
        );

        const validEntryIds = journalEntries
          .filter(entry => {
            if (!entry || entry.status !== "posted") return false;
            return entry.date <= asOfDate;
          })
          .map(entry => entry._id);

        journalLines = journalLines.filter(line => 
          validEntryIds.includes(line.journalEntryId)
        );

        // Calculate balance
        const totalDebits = journalLines.reduce((sum, line) => sum + line.debit, 0);
        const totalCredits = journalLines.reduce((sum, line) => sum + line.credit, 0);
        const calculatedBalance = totalDebits - totalCredits;

        return {
          ...cashAccount,
          accountDetails,
          calculatedBalance,
          balanceDifference: calculatedBalance - cashAccount.currentBalance,
          transactionCount: journalLines.length,
        };
      })
    );

    // Calculate totals
    const totalCashPosition = cashPositions.reduce((sum, pos) => sum + pos.calculatedBalance, 0);
    const totalRecordedBalance = cashPositions.reduce((sum, pos) => sum + pos.currentBalance, 0);

    // Group by account type
    const positionsByType = {
      cash: cashPositions.filter(pos => pos.accountType === 'cash'),
      checking: cashPositions.filter(pos => pos.accountType === 'checking'),
      savings: cashPositions.filter(pos => pos.accountType === 'savings'),
      petty_cash: cashPositions.filter(pos => pos.accountType === 'petty_cash'),
    };

    return {
      cashPositions,
      positionsByType,
      summary: {
        asOfDate,
        totalAccounts: cashPositions.length,
        totalCashPosition,
        totalRecordedBalance,
        totalDifference: totalCashPosition - totalRecordedBalance,
        generatedAt: Date.now(),
      },
      validation: {
        hasDiscrepancies: cashPositions.some(pos => Math.abs(pos.balanceDifference) > 0.01),
        discrepancies: cashPositions.filter(pos => Math.abs(pos.balanceDifference) > 0.01),
      },
    };
  },
});

// Get cash flow by account
export const getCashFlowByAccount = query({
  args: {
    accountId: v.id("accounts"),
    startDate: v.number(),
    endDate: v.number(),
  },
  handler: async (ctx, args) => {
    // Get the cash bank account
    const cashBankAccount = await ctx.db
      .query("cash_bank_accounts")
      .withIndex("by_account", (q) => q.eq("accountId", args.accountId))
      .first();

    if (!cashBankAccount) {
      throw new Error("Cash/Bank account not found");
    }

    // Get journal entry lines for this account within date range
    let journalLines = await ctx.db
      .query("journal_entry_lines")
      .withIndex("by_account", (q) => q.eq("accountId", args.accountId))
      .collect();

    // Filter by date range
    const journalEntryIds = [...new Set(journalLines.map(line => line.journalEntryId))];
    const journalEntries = await Promise.all(
      journalEntryIds.map(id => ctx.db.get(id))
    );

    const validEntryIds = journalEntries
      .filter(entry => {
        if (!entry || entry.status !== "posted") return false;
        return entry.date >= args.startDate && entry.date <= args.endDate;
      })
      .map(entry => entry._id);

    journalLines = journalLines.filter(line => 
      validEntryIds.includes(line.journalEntryId)
    );

    // Get detailed transaction information
    const transactions = await Promise.all(
      journalLines.map(async (line) => {
        const journalEntry = await ctx.db.get(line.journalEntryId);
        return {
          ...line,
          journalEntry,
          netAmount: line.debit - line.credit,
          isInflow: line.debit > line.credit,
          isOutflow: line.credit > line.debit,
        };
      })
    );

    // Sort by date
    transactions.sort((a, b) => a.journalEntry.date - b.journalEntry.date);

    // Calculate totals
    const totalInflows = transactions
      .filter(t => t.isInflow)
      .reduce((sum, t) => sum + t.netAmount, 0);

    const totalOutflows = Math.abs(transactions
      .filter(t => t.isOutflow)
      .reduce((sum, t) => sum + t.netAmount, 0));

    const netCashFlow = totalInflows - totalOutflows;

    // Group by month for trend analysis
    const monthlyData = {};
    transactions.forEach(transaction => {
      const date = new Date(transaction.journalEntry.date);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          month: monthKey,
          inflows: 0,
          outflows: 0,
          netFlow: 0,
          transactionCount: 0,
        };
      }

      const monthData = monthlyData[monthKey];
      monthData.transactionCount++;
      
      if (transaction.isInflow) {
        monthData.inflows += transaction.netAmount;
      } else {
        monthData.outflows += Math.abs(transaction.netAmount);
      }
      
      monthData.netFlow = monthData.inflows - monthData.outflows;
    });

    const monthlyTrend = Object.values(monthlyData).sort((a, b) => a.month.localeCompare(b.month));

    return {
      account: {
        ...cashBankAccount,
        accountDetails: await ctx.db.get(args.accountId),
      },
      transactions,
      monthlyTrend,
      summary: {
        startDate: args.startDate,
        endDate: args.endDate,
        totalTransactions: transactions.length,
        totalInflows,
        totalOutflows,
        netCashFlow,
        averageTransactionSize: transactions.length > 0 ? 
          (totalInflows + totalOutflows) / transactions.length : 0,
        generatedAt: Date.now(),
      },
    };
  },
});
