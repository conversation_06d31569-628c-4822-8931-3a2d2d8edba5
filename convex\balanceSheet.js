import { query } from "./_generated/server";
import { v } from "convex/values";
import { calculateAccountBalance, getAccountClassification, roundCurrency, validateDateRange } from "./utils/accounting.js";

// Generate Balance Sheet (Statement of Financial Position)
export const generateBalanceSheet = query({
  args: {
    asOfDate: v.number(),
    includeZeroBalances: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const includeZeroBalances = args.includeZeroBalances || false;

    // Get all active accounts
    const accounts = await ctx.db
      .query("accounts")
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    // Get all posted journal entries up to the as-of date
    let journalEntries = await ctx.db
      .query("journal_entries")
      .withIndex("by_status", (q) => q.eq("status", "posted"))
      .collect();

    journalEntries = journalEntries.filter(entry => entry.date <= args.asOfDate);
    const journalEntryIds = journalEntries.map(entry => entry._id);

    // Get all journal entry lines for the filtered entries
    const allJournalLines = await ctx.db
      .query("journal_entry_lines")
      .collect();

    const relevantLines = allJournalLines.filter(line =>
      journalEntryIds.includes(line.journalEntryId)
    );

    // Calculate balances for balance sheet accounts only
    const balanceSheetAccounts = accounts.filter(account => 
      ['asset', 'liability', 'equity'].includes(account.type)
    );

    const accountBalances = new Map();

    // Initialize accounts
    balanceSheetAccounts.forEach(account => {
      accountBalances.set(account._id, {
        account,
        totalDebits: 0,
        totalCredits: 0,
        balance: 0,
        classification: getAccountClassification(account.type, account.subtype),
      });
    });

    // Calculate totals
    relevantLines.forEach(line => {
      const accountData = accountBalances.get(line.accountId);
      if (accountData) {
        accountData.totalDebits += line.debit;
        accountData.totalCredits += line.credit;
      }
    });

    // Calculate final balances
    accountBalances.forEach((data, accountId) => {
      data.balance = calculateAccountBalance(
        data.account.type,
        0,
        data.totalDebits,
        data.totalCredits
      );
    });

    // Convert to array and filter zero balances if requested
    let balanceSheetData = Array.from(accountBalances.values());

    if (!includeZeroBalances) {
      balanceSheetData = balanceSheetData.filter(data => 
        Math.abs(data.balance) > 0.01
      );
    }

    // Calculate retained earnings from income statement accounts
    const incomeStatementAccounts = accounts.filter(account => 
      ['revenue', 'expense'].includes(account.type)
    );

    let retainedEarnings = 0;
    incomeStatementAccounts.forEach(account => {
      const accountLines = relevantLines.filter(line => line.accountId === account._id);
      const totalDebits = accountLines.reduce((sum, line) => sum + line.debit, 0);
      const totalCredits = accountLines.reduce((sum, line) => sum + line.credit, 0);
      const accountBalance = calculateAccountBalance(account.type, 0, totalDebits, totalCredits);
      
      if (account.type === 'revenue') {
        retainedEarnings += accountBalance;
      } else if (account.type === 'expense') {
        retainedEarnings -= accountBalance;
      }
    });

    // Organize by balance sheet sections
    const assets = {
      current: balanceSheetData.filter(data => 
        data.account.type === 'asset' && data.account.subtype === 'current_asset'
      ).sort((a, b) => a.account.code.localeCompare(b.account.code)),
      
      nonCurrent: balanceSheetData.filter(data => 
        data.account.type === 'asset' && 
        (data.account.subtype === 'non_current_asset' || !data.account.subtype)
      ).sort((a, b) => a.account.code.localeCompare(b.account.code)),
    };

    const liabilities = {
      current: balanceSheetData.filter(data => 
        data.account.type === 'liability' && data.account.subtype === 'current_liability'
      ).sort((a, b) => a.account.code.localeCompare(b.account.code)),
      
      nonCurrent: balanceSheetData.filter(data => 
        data.account.type === 'liability' && 
        (data.account.subtype === 'non_current_liability' || !data.account.subtype)
      ).sort((a, b) => a.account.code.localeCompare(b.account.code)),
    };

    const equity = balanceSheetData.filter(data => 
      data.account.type === 'equity'
    ).sort((a, b) => a.account.code.localeCompare(b.account.code));

    // Calculate totals
    const totalCurrentAssets = assets.current.reduce((sum, data) => sum + data.balance, 0);
    const totalNonCurrentAssets = assets.nonCurrent.reduce((sum, data) => sum + data.balance, 0);
    const totalAssets = totalCurrentAssets + totalNonCurrentAssets;

    const totalCurrentLiabilities = liabilities.current.reduce((sum, data) => sum + data.balance, 0);
    const totalNonCurrentLiabilities = liabilities.nonCurrent.reduce((sum, data) => sum + data.balance, 0);
    const totalLiabilities = totalCurrentLiabilities + totalNonCurrentLiabilities;

    const totalEquity = equity.reduce((sum, data) => sum + data.balance, 0) + retainedEarnings;

    const totalLiabilitiesAndEquity = totalLiabilities + totalEquity;

    // Validation - Assets should equal Liabilities + Equity
    const isBalanced = Math.abs(totalAssets - totalLiabilitiesAndEquity) < 0.01;
    const difference = totalAssets - totalLiabilitiesAndEquity;

    // Calculate financial ratios
    const currentRatio = totalCurrentLiabilities > 0 ? totalCurrentAssets / totalCurrentLiabilities : 0;
    const debtToEquityRatio = totalEquity > 0 ? totalLiabilities / totalEquity : 0;
    const workingCapital = totalCurrentAssets - totalCurrentLiabilities;

    return {
      balanceSheet: {
        assets: {
          current: assets.current,
          nonCurrent: assets.nonCurrent,
          totalCurrent: totalCurrentAssets,
          totalNonCurrent: totalNonCurrentAssets,
          total: totalAssets,
        },
        liabilities: {
          current: liabilities.current,
          nonCurrent: liabilities.nonCurrent,
          totalCurrent: totalCurrentLiabilities,
          totalNonCurrent: totalNonCurrentLiabilities,
          total: totalLiabilities,
        },
        equity: {
          accounts: equity,
          retainedEarnings,
          total: totalEquity,
        },
        totals: {
          assets: totalAssets,
          liabilities: totalLiabilities,
          equity: totalEquity,
          liabilitiesAndEquity: totalLiabilitiesAndEquity,
        },
      },
      ratios: {
        currentRatio,
        debtToEquityRatio,
        workingCapital,
      },
      summary: {
        asOfDate: args.asOfDate,
        generatedAt: Date.now(),
        totalAccounts: balanceSheetData.length,
        isBalanced,
        difference,
      },
      validation: {
        isValid: isBalanced,
        errors: isBalanced ? [] : [`Balance sheet is out of balance by ${difference.toFixed(2)}`],
        warnings: [],
      },
    };
  },
});

// Generate comparative balance sheet
export const generateComparativeBalanceSheet = query({
  args: {
    currentDate: v.number(),
    priorDate: v.number(),
    includeZeroBalances: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Generate balance sheets for both dates
    const currentBalanceSheet = await generateBalanceSheet(ctx, {
      asOfDate: args.currentDate,
      includeZeroBalances: args.includeZeroBalances,
    });

    const priorBalanceSheet = await generateBalanceSheet(ctx, {
      asOfDate: args.priorDate,
      includeZeroBalances: args.includeZeroBalances,
    });

    // Calculate changes
    const calculateChanges = (currentSection, priorSection) => {
      const changes = currentSection.map(currentItem => {
        const priorItem = priorSection.find(p => p.account._id === currentItem.account._id);
        const priorBalance = priorItem ? priorItem.balance : 0;
        const change = currentItem.balance - priorBalance;
        const percentChange = priorBalance !== 0 ? (change / Math.abs(priorBalance)) * 100 : 0;

        return {
          ...currentItem,
          priorBalance,
          change,
          percentChange,
        };
      });

      // Add accounts that existed in prior but not current
      priorSection.forEach(priorItem => {
        if (!currentSection.find(c => c.account._id === priorItem.account._id)) {
          changes.push({
            ...priorItem,
            balance: 0,
            priorBalance: priorItem.balance,
            change: -priorItem.balance,
            percentChange: -100,
          });
        }
      });

      return changes.sort((a, b) => a.account.code.localeCompare(b.account.code));
    };

    const comparativeData = {
      assets: {
        current: calculateChanges(
          currentBalanceSheet.balanceSheet.assets.current,
          priorBalanceSheet.balanceSheet.assets.current
        ),
        nonCurrent: calculateChanges(
          currentBalanceSheet.balanceSheet.assets.nonCurrent,
          priorBalanceSheet.balanceSheet.assets.nonCurrent
        ),
      },
      liabilities: {
        current: calculateChanges(
          currentBalanceSheet.balanceSheet.liabilities.current,
          priorBalanceSheet.balanceSheet.liabilities.current
        ),
        nonCurrent: calculateChanges(
          currentBalanceSheet.balanceSheet.liabilities.nonCurrent,
          priorBalanceSheet.balanceSheet.liabilities.nonCurrent
        ),
      },
      equity: calculateChanges(
        currentBalanceSheet.balanceSheet.equity.accounts,
        priorBalanceSheet.balanceSheet.equity.accounts
      ),
    };

    // Calculate total changes
    const totalChanges = {
      assets: currentBalanceSheet.balanceSheet.totals.assets - priorBalanceSheet.balanceSheet.totals.assets,
      liabilities: currentBalanceSheet.balanceSheet.totals.liabilities - priorBalanceSheet.balanceSheet.totals.liabilities,
      equity: currentBalanceSheet.balanceSheet.totals.equity - priorBalanceSheet.balanceSheet.totals.equity,
      retainedEarnings: currentBalanceSheet.balanceSheet.equity.retainedEarnings - priorBalanceSheet.balanceSheet.equity.retainedEarnings,
    };

    return {
      current: currentBalanceSheet,
      prior: priorBalanceSheet,
      comparative: comparativeData,
      changes: totalChanges,
      summary: {
        currentDate: args.currentDate,
        priorDate: args.priorDate,
        generatedAt: Date.now(),
      },
    };
  },
});
