import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import DynamicSidebar from "../../../sidebar/DynamicSidebar";
import Navbar from "../navbar/navbar";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  FileText,
  AlertCircle,
  CheckCircle,
  Factory,
  Package,
  Truck,
  Users,
  Calendar,
  PieChart,
  BarChart3,
  Calculator,
  ArrowLeft,
  Download,
  Filter,
  Search,
  Eye
} from "lucide-react";

// TypeScript interfaces for better type safety
interface Account {
  _id: string;
  code: string;
  name: string;
  type: "asset" | "liability" | "equity" | "revenue" | "expense";
  subtype?: string;
  isActive: boolean;
}

interface AccountBalance {
  account: Account;
  balance: number;
  totalDebits: number;
  totalCredits: number;
  classification: string;
}

interface DateRange {
  startDate: string;
  endDate: string;
}

type ReportType = 'overview' | 'income' | 'balance' | 'trial' | 'aging' | 'general' | 'journal' | 'manufacturing' | 'cash_flow';

function Reports() {
  const [activeReport, setActiveReport] = useState<ReportType>('overview');
  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0]!,
    endDate: new Date().toISOString().split('T')[0]!
  });

  // Use the existing Convex functions
  const balanceSheet = useQuery(
    api.balanceSheet.generateBalanceSheet,
    activeReport === "balance" ? {
      asOfDate: new Date(dateRange.endDate).getTime(),
      includeZeroBalances: false
    } : "skip"
  );

  const incomeStatement = useQuery(
    api.incomeStatement.generateIncomeStatement,
    activeReport === "income" ? {
      startDate: new Date(dateRange.startDate).getTime(),
      endDate: new Date(dateRange.endDate).getTime(),
      includeZeroBalances: false
    } : "skip"
  );

  const trialBalance = useQuery(
    api.trialBalance.generateTrialBalance,
    activeReport === "trial" ? {
      startDate: new Date(dateRange.startDate).getTime(),
      endDate: new Date(dateRange.endDate).getTime(),
      includeZeroBalances: false
    } : "skip"
  );

  const chartOfAccounts = useQuery(
    api.chartOfAccounts.getChartOfAccounts,
    { includeInactive: false }
  );

  const agingReport = useQuery(
    api.accountsPayable.getAgingSchedule,
    activeReport === "aging" ? {
      asOfDate: new Date(dateRange.endDate).getTime(),
      includeZeroBalances: false
    } : "skip"
  );

  const cashFlowStatement = useQuery(
    api.cashFlowStatement.generateCashFlowStatement,
    activeReport === "cash_flow" ? {
      startDate: new Date(dateRange.startDate).getTime(),
      endDate: new Date(dateRange.endDate).getTime()
    } : "skip"
  );

  const renderReportContent = () => {
    switch (activeReport) {
      case 'income':
        return <IncomeStatementReport data={incomeStatement} dateRange={dateRange} />;
      case 'balance':
        return <BalanceSheetReport data={balanceSheet} dateRange={dateRange} />;
      case 'trial':
        return <TrialBalanceReport data={trialBalance} dateRange={dateRange} />;
      case 'aging':
        return <AgingReport data={agingReport} dateRange={dateRange} />;
      case 'cash_flow':
        return <CashFlowReport data={cashFlowStatement} dateRange={dateRange} />;
      case 'manufacturing':
        return <ManufacturingReport dateRange={dateRange} />;
      default:
        return <ReportsOverview setActiveReport={setActiveReport} />;
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <DynamicSidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-6 bg-gradient-to-l from-teal-50 to-gray-100 flex-1 overflow-auto">
          {/* Header with Manufacturing Focus */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center space-x-3">
              <Factory className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900">XYZ Manufacturing Reports</h1>
                <p className="text-gray-600">Comprehensive financial and operational reporting</p>
              </div>
            </div>
            {activeReport !== 'overview' && (
              <div className="flex items-center space-x-4">
                {/* Date Range Selector for applicable reports */}
                {['income', 'trial', 'cash_flow', 'manufacturing'].includes(activeReport) && (
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <input
                      type="date"
                      value={dateRange.startDate}
                      onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                      className="border border-gray-300 rounded px-2 py-1 text-sm"
                    />
                    <span className="text-gray-500">to</span>
                    <input
                      type="date"
                      value={dateRange.endDate}
                      onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                      className="border border-gray-300 rounded px-2 py-1 text-sm"
                    />
                  </div>
                )}
                <button
                  type="button"
                  onClick={() => setActiveReport('overview')}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition flex items-center space-x-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  <span>Back to Overview</span>
                </button>
              </div>
            )}
          </div>

          {renderReportContent()}
        </main>
      </div>
    </div>
  );
}

function ReportsOverview({ setActiveReport }: { setActiveReport: (report: ReportType) => void }) {
  return (
    <div className="space-y-8">
      {/* Manufacturing Reports Section */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
          <Factory className="h-6 w-6 text-blue-600 mr-2" />
          Manufacturing Reports
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <ReportCard
            title="Manufacturing Cost Analysis"
            description="Track direct materials, labor, and overhead costs by product line."
            icon={<Factory className="w-8 h-8" />}
            onClick={() => setActiveReport('manufacturing')}
            color="bg-blue-50 border-blue-400 text-blue-600"
          />

          <ReportCard
            title="Production Efficiency"
            description="Monitor production metrics, capacity utilization, and efficiency ratios."
            icon={<BarChart3 className="w-8 h-8" />}
            onClick={() => {}}
            color="bg-purple-50 border-purple-400 text-purple-600"
            disabled={true}
          />

          <ReportCard
            title="Inventory Valuation"
            description="Track raw materials, work-in-progress, and finished goods inventory."
            icon={<Package className="w-8 h-8" />}
            onClick={() => {}}
            color="bg-green-50 border-green-400 text-green-600"
            disabled={true}
          />
        </div>
      </div>

      {/* Financial Reports Section */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
          <DollarSign className="h-6 w-6 text-green-600 mr-2" />
          Financial Reports
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <ReportCard
            title="Income Statement"
            description="View revenue, expenses, and profit/loss over a period."
            icon={<TrendingUp className="w-8 h-8" />}
            onClick={() => setActiveReport('income')}
            color="bg-green-50 border-green-400 text-green-600"
          />

          <ReportCard
            title="Balance Sheet"
            description="View assets, liabilities, and equity at a point in time."
            icon={<Calculator className="w-8 h-8" />}
            onClick={() => setActiveReport('balance')}
            color="bg-blue-50 border-blue-400 text-blue-600"
          />

          <ReportCard
            title="Trial Balance"
            description="View all account balances to ensure books balance."
            icon={<CheckCircle className="w-8 h-8" />}
            onClick={() => setActiveReport('trial')}
            color="bg-purple-50 border-purple-400 text-purple-600"
          />

          <ReportCard
            title="Cash Flow Statement"
            description="Track cash inflows and outflows over a period."
            icon={<TrendingDown className="w-8 h-8" />}
            onClick={() => setActiveReport('cash_flow')}
            color="bg-teal-50 border-teal-400 text-teal-600"
          />

          <ReportCard
            title="Accounts Payable Aging"
            description="View outstanding payables by age and vendor."
            icon={<AlertCircle className="w-8 h-8" />}
            onClick={() => setActiveReport('aging')}
            color="bg-orange-50 border-orange-400 text-orange-600"
          />

          <ReportCard
            title="Regulatory Reports"
            description="BIR and PFRS compliance reports for auditing."
            icon={<FileText className="w-8 h-8" />}
            onClick={() => {}}
            color="bg-red-50 border-red-400 text-red-600"
            disabled={true}
          />
        </div>
      </div>

      {/* Operational Reports Section */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
          <Truck className="h-6 w-6 text-purple-600 mr-2" />
          Operational Reports
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <ReportCard
            title="Procurement Analysis"
            description="Track supplier performance, purchase orders, and delivery metrics."
            icon={<Truck className="w-8 h-8" />}
            onClick={() => {}}
            color="bg-indigo-50 border-indigo-400 text-indigo-600"
            disabled={true}
          />

          <ReportCard
            title="Employee Productivity"
            description="Monitor labor efficiency, overtime, and productivity metrics."
            icon={<Users className="w-8 h-8" />}
            onClick={() => {}}
            color="bg-yellow-50 border-yellow-400 text-yellow-600"
            disabled={true}
          />

          <ReportCard
            title="Quality Control"
            description="Track defect rates, quality metrics, and compliance standards."
            icon={<Eye className="w-8 h-8" />}
            onClick={() => {}}
            color="bg-pink-50 border-pink-400 text-pink-600"
            disabled={true}
          />
        </div>
      </div>
    </div>
  );
}

function ReportCard({
  title,
  description,
  icon,
  onClick,
  color,
  disabled = false
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick: () => void;
  color: string;
  disabled?: boolean;
}) {
  return (
    <div className={`${color} border-2 rounded-xl shadow-lg p-6 transition-all ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-xl cursor-pointer'}`}>
      <div className="flex items-center mb-4">
        <div className="mr-4">
          {icon}
        </div>
        <h3 className="text-lg font-semibold">{title}</h3>
      </div>
      <p className="text-sm mb-4 opacity-80">
        {description}
      </p>
      <button
        type="button"
        onClick={onClick}
        disabled={disabled}
        className={`w-full px-4 py-2 rounded transition ${
          disabled
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
        }`}
      >
        {disabled ? 'Coming Soon' : 'View Report'}
      </button>
    </div>
  );
}

function IncomeStatementReport({ data, dateRange }: { data: any; dateRange: DateRange }) {
  if (!data) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading Income Statement...</p>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      {/* Header */}
      <div className="text-center mb-8 border-b border-gray-200 pb-6">
        <div className="flex items-center justify-center mb-2">
          <Factory className="h-8 w-8 text-blue-600 mr-3" />
          <h2 className="text-3xl font-bold text-gray-800">XYZ Manufacturing</h2>
        </div>
        <h3 className="text-xl font-semibold text-gray-700">Statement of Comprehensive Income</h3>
        <p className="text-gray-600">
          For the Period from {new Date(dateRange.startDate).toLocaleDateString()} to {new Date(dateRange.endDate).toLocaleDateString()}
        </p>
        <div className="mt-2 text-sm text-gray-500">
          (Amounts in Philippine Pesos - PFRS Compliant)
        </div>
      </div>

      <div className="space-y-8">
        {/* Revenue Section */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2 flex items-center">
            <TrendingUp className="h-5 w-5 text-green-600 mr-2" />
            Revenue
          </h3>
          <div className="space-y-3">
            {/* Operating Revenue */}
            <div className="ml-4">
              <h4 className="font-medium text-gray-700 mb-2">Operating Revenue</h4>
              {data.incomeStatement.revenue.operating.map((item: any, index: number) => (
                <div key={index} className="flex justify-between items-center py-2 ml-4">
                  <div>
                    <span className="text-gray-700">{item.account.name}</span>
                    <span className="text-gray-500 text-sm ml-2">({item.account.code})</span>
                  </div>
                  <span className="font-medium text-green-600">{formatCurrency(Math.abs(item.balance))}</span>
                </div>
              ))}
              <div className="flex justify-between items-center py-2 ml-4 border-t border-gray-100 font-medium">
                <span className="text-gray-700">Total Operating Revenue</span>
                <span className="text-green-600">{formatCurrency(data.incomeStatement.revenue.totalOperating)}</span>
              </div>
            </div>

            {/* Other Revenue */}
            {data.incomeStatement.revenue.other.length > 0 && (
              <div className="ml-4">
                <h4 className="font-medium text-gray-700 mb-2">Other Revenue</h4>
                {data.incomeStatement.revenue.other.map((item: any, index: number) => (
                  <div key={index} className="flex justify-between items-center py-2 ml-4">
                    <div>
                      <span className="text-gray-700">{item.account.name}</span>
                      <span className="text-gray-500 text-sm ml-2">({item.account.code})</span>
                    </div>
                    <span className="font-medium text-green-600">{formatCurrency(Math.abs(item.balance))}</span>
                  </div>
                ))}
                <div className="flex justify-between items-center py-2 ml-4 border-t border-gray-100 font-medium">
                  <span className="text-gray-700">Total Other Revenue</span>
                  <span className="text-green-600">{formatCurrency(data.incomeStatement.revenue.totalOther)}</span>
                </div>
              </div>
            )}

            <div className="flex justify-between items-center py-3 border-t-2 border-gray-300 font-bold text-lg">
              <span className="text-gray-800">TOTAL REVENUE</span>
              <span className="text-green-600">{formatCurrency(data.incomeStatement.revenue.total)}</span>
            </div>
          </div>
        </div>

        {/* Expenses Section */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2 flex items-center">
            <TrendingDown className="h-5 w-5 text-red-600 mr-2" />
            Expenses
          </h3>
          <div className="space-y-3">
            {/* Cost of Sales */}
            {data.incomeStatement.expenses.costOfSales.length > 0 && (
              <div className="ml-4">
                <h4 className="font-medium text-gray-700 mb-2">Cost of Sales</h4>
                {data.incomeStatement.expenses.costOfSales.map((item: any, index: number) => (
                  <div key={index} className="flex justify-between items-center py-2 ml-4">
                    <div>
                      <span className="text-gray-700">{item.account.name}</span>
                      <span className="text-gray-500 text-sm ml-2">({item.account.code})</span>
                    </div>
                    <span className="font-medium text-red-600">{formatCurrency(Math.abs(item.balance))}</span>
                  </div>
                ))}
              </div>
            )}

            {/* Operating Expenses */}
            {data.incomeStatement.expenses.operating.length > 0 && (
              <div className="ml-4">
                <h4 className="font-medium text-gray-700 mb-2">Operating Expenses</h4>
                {data.incomeStatement.expenses.operating.map((item: any, index: number) => (
                  <div key={index} className="flex justify-between items-center py-2 ml-4">
                    <div>
                      <span className="text-gray-700">{item.account.name}</span>
                      <span className="text-gray-500 text-sm ml-2">({item.account.code})</span>
                    </div>
                    <span className="font-medium text-red-600">{formatCurrency(Math.abs(item.balance))}</span>
                  </div>
                ))}
              </div>
            )}

            <div className="flex justify-between items-center py-3 border-t-2 border-gray-300 font-bold text-lg">
              <span className="text-gray-800">TOTAL EXPENSES</span>
              <span className="text-red-600">{formatCurrency(data.incomeStatement.expenses.total)}</span>
            </div>
          </div>
        </div>

        {/* Net Income */}
        <div className="border-t-4 border-blue-600 pt-6">
          <div className="flex justify-between items-center py-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg px-6">
            <span className="text-2xl font-bold text-gray-800">NET INCOME</span>
            <span className={`text-2xl font-bold ${
              (data.incomeStatement.revenue.total - data.incomeStatement.expenses.total) >= 0
                ? 'text-green-600'
                : 'text-red-600'
            }`}>
              {formatCurrency(data.incomeStatement.revenue.total - data.incomeStatement.expenses.total)}
            </span>
          </div>
        </div>

        {/* Export Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
          >
            <Download className="h-4 w-4" />
            <span>Export PDF</span>
          </button>
          <button
            type="button"
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition"
          >
            <Download className="h-4 w-4" />
            <span>Export Excel</span>
          </button>
        </div>
      </div>
    </div>
  );
}

function BalanceSheetReport({ data, dateRange }: { data: any; dateRange: DateRange }) {
  if (!data) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading Balance Sheet...</p>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      {/* Header */}
      <div className="text-center mb-8 border-b border-gray-200 pb-6">
        <div className="flex items-center justify-center mb-2">
          <Factory className="h-8 w-8 text-blue-600 mr-3" />
          <h2 className="text-3xl font-bold text-gray-800">XYZ Manufacturing</h2>
        </div>
        <h3 className="text-xl font-semibold text-gray-700">Statement of Financial Position</h3>
        <p className="text-gray-600">As of {new Date(dateRange.endDate).toLocaleDateString()}</p>
        <div className="mt-2 text-sm text-gray-500">
          (Amounts in Philippine Pesos - PFRS Compliant)
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Assets */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2 flex items-center">
            <Package className="h-5 w-5 text-blue-600 mr-2" />
            Assets
          </h3>

          {/* Current Assets */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-700 mb-3">Current Assets</h4>
            <div className="space-y-2 ml-4">
              {data.balanceSheet.assets.current.map((item: any, index: number) => (
                <div key={index} className="flex justify-between items-center py-1">
                  <div>
                    <span className="text-gray-700">{item.account.name}</span>
                    <span className="text-gray-500 text-sm ml-2">({item.account.code})</span>
                  </div>
                  <span className="font-medium">{formatCurrency(Math.abs(item.balance))}</span>
                </div>
              ))}
              <div className="flex justify-between items-center py-2 border-t border-gray-200 font-semibold">
                <span>Total Current Assets</span>
                <span>{formatCurrency(data.balanceSheet.totals.totalCurrentAssets)}</span>
              </div>
            </div>
          </div>

          {/* Non-Current Assets */}
          <div>
            <h4 className="font-medium text-gray-700 mb-3">Non-Current Assets</h4>
            <div className="space-y-2 ml-4">
              {data.balanceSheet.assets.nonCurrent.map((item: any, index: number) => (
                <div key={index} className="flex justify-between items-center py-1">
                  <div>
                    <span className="text-gray-700">{item.account.name}</span>
                    <span className="text-gray-500 text-sm ml-2">({item.account.code})</span>
                  </div>
                  <span className="font-medium">{formatCurrency(Math.abs(item.balance))}</span>
                </div>
              ))}
              <div className="flex justify-between items-center py-2 border-t border-gray-200 font-semibold">
                <span>Total Non-Current Assets</span>
                <span>{formatCurrency(data.balanceSheet.totals.totalNonCurrentAssets)}</span>
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center py-3 border-t-2 border-blue-600 font-bold text-lg bg-blue-50 rounded px-4">
            <span>TOTAL ASSETS</span>
            <span>{formatCurrency(data.balanceSheet.totals.totalAssets)}</span>
          </div>
        </div>

        {/* Liabilities & Equity */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2 flex items-center">
            <AlertCircle className="h-5 w-5 text-orange-600 mr-2" />
            Liabilities
          </h3>

          {/* Current Liabilities */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-700 mb-3">Current Liabilities</h4>
            <div className="space-y-2 ml-4">
              {data.balanceSheet.liabilities.current.map((item: any, index: number) => (
                <div key={index} className="flex justify-between items-center py-1">
                  <div>
                    <span className="text-gray-700">{item.account.name}</span>
                    <span className="text-gray-500 text-sm ml-2">({item.account.code})</span>
                  </div>
                  <span className="font-medium text-orange-600">{formatCurrency(Math.abs(item.balance))}</span>
                </div>
              ))}
              <div className="flex justify-between items-center py-2 border-t border-gray-200 font-semibold">
                <span>Total Current Liabilities</span>
                <span className="text-orange-600">{formatCurrency(data.balanceSheet.totals.totalCurrentLiabilities)}</span>
              </div>
            </div>
          </div>

          {/* Non-Current Liabilities */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-700 mb-3">Non-Current Liabilities</h4>
            <div className="space-y-2 ml-4">
              {data.balanceSheet.liabilities.nonCurrent.map((item: any, index: number) => (
                <div key={index} className="flex justify-between items-center py-1">
                  <div>
                    <span className="text-gray-700">{item.account.name}</span>
                    <span className="text-gray-500 text-sm ml-2">({item.account.code})</span>
                  </div>
                  <span className="font-medium text-orange-600">{formatCurrency(Math.abs(item.balance))}</span>
                </div>
              ))}
              <div className="flex justify-between items-center py-2 border-t border-gray-200 font-semibold">
                <span>Total Non-Current Liabilities</span>
                <span className="text-orange-600">{formatCurrency(data.balanceSheet.totals.totalNonCurrentLiabilities)}</span>
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center py-2 border-t border-gray-300 font-bold">
            <span>TOTAL LIABILITIES</span>
            <span className="text-orange-600">{formatCurrency(data.balanceSheet.totals.totalLiabilities)}</span>
          </div>

          {/* Equity */}
          <div className="mt-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2 flex items-center">
              <DollarSign className="h-5 w-5 text-green-600 mr-2" />
              Equity
            </h3>
            <div className="space-y-2 ml-4">
              {data.balanceSheet.equity.accounts.map((item: any, index: number) => (
                <div key={index} className="flex justify-between items-center py-1">
                  <div>
                    <span className="text-gray-700">{item.account.name}</span>
                    <span className="text-gray-500 text-sm ml-2">({item.account.code})</span>
                  </div>
                  <span className="font-medium text-green-600">{formatCurrency(Math.abs(item.balance))}</span>
                </div>
              ))}
              <div className="flex justify-between items-center py-1">
                <span className="text-gray-700">Retained Earnings</span>
                <span className="font-medium text-green-600">{formatCurrency(data.balanceSheet.equity.retainedEarnings)}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-t border-gray-200 font-semibold">
                <span>Total Equity</span>
                <span className="text-green-600">{formatCurrency(data.balanceSheet.totals.equity)}</span>
              </div>
            </div>
          </div>

          <div className="mt-6 pt-4 border-t-2 border-green-600">
            <div className="flex justify-between items-center py-3 bg-green-50 rounded-lg px-4 font-bold text-lg">
              <span className="text-gray-800">TOTAL LIABILITIES & EQUITY</span>
              <span className="text-gray-800">{formatCurrency(data.balanceSheet.totals.liabilitiesAndEquity)}</span>
            </div>
          </div>

          {/* Balance Check */}
          <div className="mt-4">
            <div className={`flex justify-between items-center py-2 px-4 rounded-lg ${
              data.summary.isBalanced ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              <span className="font-medium">
                {data.summary.isBalanced ? '✓ Balance Sheet is balanced' : '⚠ Balance Sheet is not balanced'}
              </span>
              {!data.summary.isBalanced && (
                <span className="font-bold">
                  Difference: {formatCurrency(Math.abs(data.summary.difference))}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Export Actions */}
      <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 mt-8">
        <button
          type="button"
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
        >
          <Download className="h-4 w-4" />
          <span>Export PDF</span>
        </button>
        <button
          type="button"
          className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition"
        >
          <Download className="h-4 w-4" />
          <span>Export Excel</span>
        </button>
      </div>
    </div>
  );
}

function CashFlowReport({ data, dateRange }: { data: any; dateRange: DateRange }) {
  if (!data) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading Cash Flow Statement...</p>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      {/* Header */}
      <div className="text-center mb-8 border-b border-gray-200 pb-6">
        <div className="flex items-center justify-center mb-2">
          <Factory className="h-8 w-8 text-blue-600 mr-3" />
          <h2 className="text-3xl font-bold text-gray-800">XYZ Manufacturing</h2>
        </div>
        <h3 className="text-xl font-semibold text-gray-700">Statement of Cash Flows</h3>
        <p className="text-gray-600">
          For the Period from {new Date(dateRange.startDate).toLocaleDateString()} to {new Date(dateRange.endDate).toLocaleDateString()}
        </p>
        <div className="mt-2 text-sm text-gray-500">
          (Amounts in Philippine Pesos - PFRS Compliant)
        </div>
      </div>

      <div className="space-y-8">
        {/* Operating Activities */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2 flex items-center">
            <TrendingUp className="h-5 w-5 text-green-600 mr-2" />
            Cash Flows from Operating Activities
          </h3>
          <div className="space-y-2 ml-4">
            {data.operatingActivities?.map((item: any, index: number) => (
              <div key={index} className="flex justify-between items-center py-1">
                <span className="text-gray-700">{item.description}</span>
                <span className={`font-medium ${item.amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(Math.abs(item.amount))}
                </span>
              </div>
            ))}
            <div className="flex justify-between items-center py-3 border-t-2 border-gray-300 font-bold">
              <span>Net Cash from Operating Activities</span>
              <span className={data.netOperatingCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}>
                {formatCurrency(Math.abs(data.netOperatingCashFlow))}
              </span>
            </div>
          </div>
        </div>

        {/* Investing Activities */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2 flex items-center">
            <Package className="h-5 w-5 text-blue-600 mr-2" />
            Cash Flows from Investing Activities
          </h3>
          <div className="space-y-2 ml-4">
            {data.investingActivities?.map((item: any, index: number) => (
              <div key={index} className="flex justify-between items-center py-1">
                <span className="text-gray-700">{item.description}</span>
                <span className={`font-medium ${item.amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(Math.abs(item.amount))}
                </span>
              </div>
            ))}
            <div className="flex justify-between items-center py-3 border-t-2 border-gray-300 font-bold">
              <span>Net Cash from Investing Activities</span>
              <span className={data.netInvestingCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}>
                {formatCurrency(Math.abs(data.netInvestingCashFlow))}
              </span>
            </div>
          </div>
        </div>

        {/* Financing Activities */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2 flex items-center">
            <DollarSign className="h-5 w-5 text-purple-600 mr-2" />
            Cash Flows from Financing Activities
          </h3>
          <div className="space-y-2 ml-4">
            {data.financingActivities?.map((item: any, index: number) => (
              <div key={index} className="flex justify-between items-center py-1">
                <span className="text-gray-700">{item.description}</span>
                <span className={`font-medium ${item.amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(Math.abs(item.amount))}
                </span>
              </div>
            ))}
            <div className="flex justify-between items-center py-3 border-t-2 border-gray-300 font-bold">
              <span>Net Cash from Financing Activities</span>
              <span className={data.netFinancingCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}>
                {formatCurrency(Math.abs(data.netFinancingCashFlow))}
              </span>
            </div>
          </div>
        </div>

        {/* Net Change in Cash */}
        <div className="border-t-4 border-blue-600 pt-6">
          <div className="space-y-3">
            <div className="flex justify-between items-center py-2">
              <span className="text-lg font-semibold">Net Increase (Decrease) in Cash</span>
              <span className={`text-lg font-bold ${data.netCashChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatCurrency(Math.abs(data.netCashChange))}
              </span>
            </div>
            <div className="flex justify-between items-center py-2">
              <span className="text-gray-700">Cash at Beginning of Period</span>
              <span className="font-medium">{formatCurrency(data.beginningCash)}</span>
            </div>
            <div className="flex justify-between items-center py-3 bg-blue-50 rounded-lg px-4 font-bold text-xl">
              <span>Cash at End of Period</span>
              <span className="text-blue-600">{formatCurrency(data.endingCash)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Export Actions */}
      <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 mt-8">
        <button
          type="button"
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
        >
          <Download className="h-4 w-4" />
          <span>Export PDF</span>
        </button>
        <button
          type="button"
          className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition"
        >
          <Download className="h-4 w-4" />
          <span>Export Excel</span>
        </button>
      </div>
    </div>
  );
}

function ManufacturingReport({ dateRange }: { dateRange: DateRange }) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  // Mock data for manufacturing report
  const manufacturingData = {
    directMaterials: 850000,
    directLabor: 420000,
    manufacturingOverhead: 310000,
    totalManufacturingCosts: 1580000,
    productLines: [
      { name: 'Product Line A', cost: 680000, percentage: 43 },
      { name: 'Product Line B', cost: 520000, percentage: 33 },
      { name: 'Product Line C', cost: 380000, percentage: 24 }
    ],
    efficiency: {
      laborEfficiency: 92,
      materialUtilization: 88,
      overheadAbsorption: 95
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      {/* Header */}
      <div className="text-center mb-8 border-b border-gray-200 pb-6">
        <div className="flex items-center justify-center mb-2">
          <Factory className="h-8 w-8 text-blue-600 mr-3" />
          <h2 className="text-3xl font-bold text-gray-800">XYZ Manufacturing</h2>
        </div>
        <h3 className="text-xl font-semibold text-gray-700">Manufacturing Cost Analysis Report</h3>
        <p className="text-gray-600">
          For the Period from {new Date(dateRange.startDate).toLocaleDateString()} to {new Date(dateRange.endDate).toLocaleDateString()}
        </p>
      </div>

      <div className="space-y-8">
        {/* Cost Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-semibold text-blue-900">Direct Materials</h4>
                <p className="text-2xl font-bold text-blue-700">{formatCurrency(manufacturingData.directMaterials)}</p>
                <p className="text-sm text-blue-600">Raw materials consumed</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-semibold text-green-900">Direct Labor</h4>
                <p className="text-2xl font-bold text-green-700">{formatCurrency(manufacturingData.directLabor)}</p>
                <p className="text-sm text-green-600">Production wages</p>
              </div>
              <Users className="h-8 w-8 text-green-600" />
            </div>
          </div>

          <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-semibold text-purple-900">Manufacturing Overhead</h4>
                <p className="text-2xl font-bold text-purple-700">{formatCurrency(manufacturingData.manufacturingOverhead)}</p>
                <p className="text-sm text-purple-600">Indirect costs</p>
              </div>
              <Truck className="h-8 w-8 text-purple-600" />
            </div>
          </div>
        </div>

        {/* Total Manufacturing Costs */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-bold text-gray-800">Total Manufacturing Costs</h3>
            <span className="text-3xl font-bold text-blue-600">{formatCurrency(manufacturingData.totalManufacturingCosts)}</span>
          </div>
        </div>

        {/* Product Line Breakdown */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
            Cost Breakdown by Product Line
          </h3>
          <div className="space-y-4">
            {manufacturingData.productLines.map((line, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium text-gray-900">{line.name}</span>
                  <span className="font-bold text-gray-800">{formatCurrency(line.cost)} ({line.percentage}%)</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`bg-blue-600 h-2 rounded-full ${
                      line.percentage >= 40 ? 'w-2/5' :
                      line.percentage >= 30 ? 'w-1/3' : 'w-1/4'
                    }`}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Efficiency Metrics */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
            Manufacturing Efficiency Metrics
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{manufacturingData.efficiency.laborEfficiency}%</div>
              <div className="text-sm text-gray-600">Labor Efficiency</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">{manufacturingData.efficiency.materialUtilization}%</div>
              <div className="text-sm text-gray-600">Material Utilization</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">{manufacturingData.efficiency.overheadAbsorption}%</div>
              <div className="text-sm text-gray-600">Overhead Absorption</div>
            </div>
          </div>
        </div>
      </div>

      {/* Export Actions */}
      <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 mt-8">
        <button
          type="button"
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
        >
          <Download className="h-4 w-4" />
          <span>Export PDF</span>
        </button>
        <button
          type="button"
          className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition"
        >
          <Download className="h-4 w-4" />
          <span>Export Excel</span>
        </button>
      </div>
    </div>
  );
}

function TrialBalanceReport({ data, dateRange }: { data: any; dateRange: DateRange }) {
  if (!data || !data.summary) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading Trial Balance...</p>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  const { trialBalance, summary } = data;

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      {/* Header */}
      <div className="text-center mb-8 border-b border-gray-200 pb-6">
        <div className="flex items-center justify-center mb-2">
          <Factory className="h-8 w-8 text-blue-600 mr-3" />
          <h2 className="text-3xl font-bold text-gray-800">XYZ Manufacturing</h2>
        </div>
        <h3 className="text-xl font-semibold text-gray-700">Trial Balance</h3>
        <p className="text-gray-600">
          For the Period from {new Date(dateRange.startDate).toLocaleDateString()} to {new Date(dateRange.endDate).toLocaleDateString()}
        </p>
        <div className="mt-2 text-sm text-gray-500">
          (Amounts in Philippine Pesos - PFRS Compliant)
        </div>
        <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium mt-4 ${
          summary.isBalanced ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {summary.isBalanced ? '✓ Books are balanced' : '⚠ Books are not balanced'}
          {!summary.isBalanced && (
            <span className="ml-2">Difference: {formatCurrency(Math.abs(summary.difference))}</span>
          )}
        </div>
      </div>

      {trialBalance && trialBalance.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b-2 border-gray-300">
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Account Code</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Account Name</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700">Type</th>
                <th className="text-right py-3 px-4 font-semibold text-gray-700">Debit Balance</th>
                <th className="text-right py-3 px-4 font-semibold text-gray-700">Credit Balance</th>
              </tr>
            </thead>
            <tbody>
              {trialBalance.map((item: any, index: number) => (
                <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4 text-sm font-medium text-gray-900">{item.account.code}</td>
                  <td className="py-3 px-4 text-sm text-gray-900">{item.account.name}</td>
                  <td className="py-3 px-4 text-sm">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      item.account.type === 'asset' ? 'bg-blue-100 text-blue-800' :
                      item.account.type === 'liability' ? 'bg-red-100 text-red-800' :
                      item.account.type === 'equity' ? 'bg-purple-100 text-purple-800' :
                      item.account.type === 'revenue' ? 'bg-green-100 text-green-800' :
                      'bg-orange-100 text-orange-800'
                    }`}>
                      {item.account.type.charAt(0).toUpperCase() + item.account.type.slice(1)}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-sm text-right">
                    {item.totalDebits > 0 ? (
                      <span className="text-red-600 font-medium">{formatCurrency(item.totalDebits)}</span>
                    ) : (
                      <span className="text-gray-400">—</span>
                    )}
                  </td>
                  <td className="py-3 px-4 text-sm text-right">
                    {item.totalCredits > 0 ? (
                      <span className="text-green-600 font-medium">{formatCurrency(item.totalCredits)}</span>
                    ) : (
                      <span className="text-gray-400">—</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr className="border-t-2 border-gray-300 bg-gray-50">
                <td colSpan={3} className="py-4 px-4 font-bold text-gray-800 text-lg">TOTALS</td>
                <td className="py-4 px-4 text-right font-bold text-red-600 text-lg">{formatCurrency(summary.totalDebits)}</td>
                <td className="py-4 px-4 text-right font-bold text-green-600 text-lg">{formatCurrency(summary.totalCredits)}</td>
              </tr>
            </tfoot>
          </table>
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-4">No account activity found</div>
          <p className="text-gray-600">
            Create accounts and record transactions to see your trial balance.
          </p>
        </div>
      )}

      {/* Export Actions */}
      <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 mt-8">
        <button
          type="button"
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
        >
          <Download className="h-4 w-4" />
          <span>Export PDF</span>
        </button>
        <button
          type="button"
          className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition"
        >
          <Download className="h-4 w-4" />
          <span>Export Excel</span>
        </button>
      </div>
    </div>
  );
}

function AgingReport({ data, dateRange }: { data: any; dateRange: DateRange }) {
  if (!data) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading Aging Report...</p>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      {/* Header */}
      <div className="text-center mb-8 border-b border-gray-200 pb-6">
        <div className="flex items-center justify-center mb-2">
          <Factory className="h-8 w-8 text-blue-600 mr-3" />
          <h2 className="text-3xl font-bold text-gray-800">XYZ Manufacturing</h2>
        </div>
        <h3 className="text-xl font-semibold text-gray-700">Accounts Payable Aging Report</h3>
        <p className="text-gray-600">As of {new Date(dateRange.endDate).toLocaleDateString()}</p>
        <div className="mt-2 text-sm text-gray-500">
          (Amounts in Philippine Pesos - PFRS Compliant)
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-green-600">${data.summary.current.toLocaleString()}</div>
          <div className="text-sm text-gray-600">Current</div>
        </div>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-yellow-600">${data.summary.days1to30.toLocaleString()}</div>
          <div className="text-sm text-gray-600">1-30 Days</div>
        </div>
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-orange-600">${data.summary.days31to60.toLocaleString()}</div>
          <div className="text-sm text-gray-600">31-60 Days</div>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-red-600">${data.summary.days61to90.toLocaleString()}</div>
          <div className="text-sm text-gray-600">61-90 Days</div>
        </div>
        <div className="bg-red-100 border border-red-300 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-red-700">${data.summary.over90Days.toLocaleString()}</div>
          <div className="text-sm text-gray-600">Over 90 Days</div>
        </div>
      </div>

      <div className="mb-6">
        <div className="flex justify-between items-center py-3 bg-gray-50 rounded-lg px-4">
          <span className="font-bold text-gray-800">Total Outstanding</span>
          <span className="font-bold text-gray-800">${data.totalOutstanding.toLocaleString()}</span>
        </div>
      </div>

      {/* Detailed Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b-2 border-gray-300">
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Customer</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Invoice #</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Date Issued</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Due Date</th>
              <th className="text-right py-3 px-4 font-semibold text-gray-700">Amount</th>
              <th className="text-center py-3 px-4 font-semibold text-gray-700">Days Past Due</th>
              <th className="text-center py-3 px-4 font-semibold text-gray-700">Status</th>
            </tr>
          </thead>
          <tbody>
            {data.invoices.map((invoice: any, index: number) => (
              <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-3 px-4 text-sm font-medium text-gray-900">{invoice.customerName}</td>
                <td className="py-3 px-4 text-sm text-gray-900">{invoice.invoiceNumber}</td>
                <td className="py-3 px-4 text-sm text-gray-600">{new Date(invoice.dateIssued).toLocaleDateString()}</td>
                <td className="py-3 px-4 text-sm text-gray-600">{new Date(invoice.dueDate).toLocaleDateString()}</td>
                <td className="py-3 px-4 text-sm text-right font-medium">${invoice.amount.toLocaleString()}</td>
                <td className="py-3 px-4 text-sm text-center">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    invoice.daysPastDue <= 0 ? 'bg-green-100 text-green-800' :
                    invoice.daysPastDue <= 30 ? 'bg-yellow-100 text-yellow-800' :
                    invoice.daysPastDue <= 60 ? 'bg-orange-100 text-orange-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {invoice.daysPastDue > 0 ? `${invoice.daysPastDue} days` : 'Current'}
                  </span>
                </td>
                <td className="py-3 px-4 text-sm text-center">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    invoice.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {invoice.status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

function GeneralLedgerReport({ data }: { data: any }) {
  const [selectedAccountId, setSelectedAccountId] = useState<string>("");

  if (!data) {
    return <div className="bg-white rounded-xl shadow-lg p-8 text-center">Loading...</div>;
  }

  const selectedLedger = selectedAccountId
    ? data.find((ledger: any) => ledger.account._id === selectedAccountId)
    : null;

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-800">General Ledger</h2>
          <p className="text-gray-600">As of {new Date().toLocaleDateString()}</p>
        </div>

        {/* Account Selector */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Account to View Ledger
          </label>
          <select
            value={selectedAccountId}
            onChange={(e) => setSelectedAccountId(e.target.value)}
            className="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Choose an account...</option>
            {data.map((ledger: any) => (
              <option key={ledger.account._id} value={ledger.account._id}>
                {ledger.account.code} - {ledger.account.name}
              </option>
            ))}
          </select>
        </div>

        {!selectedLedger ? (
          /* Account Summary Cards */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {data.map((ledger: any) => (
              <div
                key={ledger.account._id}
                className="bg-gray-50 rounded-lg border border-gray-200 p-6 cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => setSelectedAccountId(ledger.account._id)}
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{ledger.account.name}</h3>
                    <p className="text-sm text-gray-500">{ledger.account.code}</p>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    ledger.account.type === 'Asset' ? 'bg-blue-100 text-blue-800' :
                    ledger.account.type === 'Liability' ? 'bg-red-100 text-red-800' :
                    ledger.account.type === 'Equity' ? 'bg-purple-100 text-purple-800' :
                    ledger.account.type === 'Revenue' ? 'bg-green-100 text-green-800' :
                    'bg-orange-100 text-orange-800'
                  }`}>
                    {ledger.account.type}
                  </span>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Entries:</span>
                    <span className="text-sm font-medium">{ledger.entries.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Balance:</span>
                    <span className={`text-sm font-bold ${
                      ledger.finalBalance >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      ${Math.abs(ledger.finalBalance).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          /* Selected Account Ledger */
          <div className="space-y-6">
            {/* Account Header */}
            <div className="bg-gray-50 rounded-lg p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{selectedLedger.account.name}</h3>
                  <p className="text-gray-600">Account Code: {selectedLedger.account.code}</p>
                  <p className="text-gray-600">Account Type: {selectedLedger.account.type}</p>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-600">Current Balance</div>
                  <div className={`text-2xl font-bold ${
                    selectedLedger.finalBalance >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    ${Math.abs(selectedLedger.finalBalance).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>

            {/* Ledger Entries */}
            <div>
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-lg font-semibold">Transaction History</h4>
                <button
                  type="button"
                  onClick={() => setSelectedAccountId("")}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  ← Back to All Accounts
                </button>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b-2 border-gray-300">
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Date</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Description</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Reference</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Debit</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Credit</th>
                      <th className="text-right py-3 px-4 font-semibold text-gray-700">Balance</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedLedger.entries.map((entry: any, index: number) => (
                      <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4 text-sm">
                          {new Date(entry.date).toLocaleDateString()}
                        </td>
                        <td className="py-3 px-4 text-sm">{entry.description}</td>
                        <td className="py-3 px-4 text-sm font-mono">{entry.reference || '—'}</td>
                        <td className="py-3 px-4 text-right font-mono">
                          {entry.debit > 0 ? (
                            <span className="text-red-600">${entry.debit.toLocaleString()}</span>
                          ) : (
                            <span className="text-gray-400">—</span>
                          )}
                        </td>
                        <td className="py-3 px-4 text-right font-mono">
                          {entry.credit > 0 ? (
                            <span className="text-green-600">${entry.credit.toLocaleString()}</span>
                          ) : (
                            <span className="text-gray-400">—</span>
                          )}
                        </td>
                        <td className="py-3 px-4 text-right font-mono font-medium">
                          <span className={entry.runningBalance >= 0 ? 'text-green-600' : 'text-red-600'}>
                            ${Math.abs(entry.runningBalance).toLocaleString()}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function JournalEntriesReport({ data }: { data: any }) {
  if (!data) {
    return <div className="bg-white rounded-xl shadow-lg p-8 text-center">Loading...</div>;
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-800">Journal Entries</h2>
        <p className="text-gray-600">All recorded transactions</p>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b-2 border-gray-300">
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Date</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Description</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Reference</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-700">Type</th>
              <th className="text-right py-3 px-4 font-semibold text-gray-700">Debit</th>
              <th className="text-right py-3 px-4 font-semibold text-gray-700">Credit</th>
            </tr>
          </thead>
          <tbody>
            {data.map((entry: any, index: number) => (
              <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-3 px-4 text-sm">
                  {new Date(entry.date).toLocaleDateString()}
                </td>
                <td className="py-3 px-4 text-sm">{entry.description}</td>
                <td className="py-3 px-4 text-sm font-mono">{entry.reference || '—'}</td>
                <td className="py-3 px-4 text-sm">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    entry.transactionType === 'Sales' ? 'bg-green-100 text-green-800' :
                    entry.transactionType === 'Purchase' ? 'bg-blue-100 text-blue-800' :
                    entry.transactionType === 'Production' ? 'bg-purple-100 text-purple-800' :
                    entry.transactionType === 'Payroll' ? 'bg-orange-100 text-orange-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {entry.transactionType || 'General'}
                  </span>
                </td>
                <td className="py-3 px-4 text-right font-mono">
                  {entry.debit > 0 ? (
                    <span className="text-red-600 font-medium">${entry.debit.toLocaleString()}</span>
                  ) : (
                    <span className="text-gray-400">—</span>
                  )}
                </td>
                <td className="py-3 px-4 text-right font-mono">
                  {entry.credit > 0 ? (
                    <span className="text-green-600 font-medium">${entry.credit.toLocaleString()}</span>
                  ) : (
                    <span className="text-gray-400">—</span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {data.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-4">No journal entries found</div>
          <p className="text-gray-600">
            Journal entries will appear here when transactions are recorded.
          </p>
        </div>
      )}
    </div>
  );
}

export default Reports;
