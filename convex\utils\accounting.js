// Utility functions for accounting calculations and validations

/**
 * Validates that debits equal credits in a journal entry
 */
export function validateDoubleEntry(lines) {
  const totalDebits = lines.reduce((sum, line) => sum + (line.debit || 0), 0);
  const totalCredits = lines.reduce((sum, line) => sum + (line.credit || 0), 0);
  
  return {
    isValid: Math.abs(totalDebits - totalCredits) < 0.01, // Allow for rounding
    totalDebits,
    totalCredits,
    difference: totalDebits - totalCredits
  };
}

/**
 * Generates next journal entry number
 */
export function generateEntryNumber(lastEntryNumber) {
  if (!lastEntryNumber) {
    return "JE-000001";
  }
  
  const match = lastEntryNumber.match(/JE-(\d+)/);
  if (match) {
    const nextNumber = parseInt(match[1]) + 1;
    return `JE-${nextNumber.toString().padStart(6, '0')}`;
  }
  
  return "JE-000001";
}

/**
 * Calculates account balance based on account type and transactions
 */
export function calculateAccountBalance(accountType, openingBalance, debits, credits) {
  const netDebits = debits || 0;
  const netCredits = credits || 0;
  const opening = openingBalance || 0;
  
  // Normal debit balance accounts: Assets, Expenses
  if (accountType === 'asset' || accountType === 'expense') {
    return opening + netDebits - netCredits;
  }
  
  // Normal credit balance accounts: Liabilities, Equity, Revenue
  if (accountType === 'liability' || accountType === 'equity' || accountType === 'revenue') {
    return opening + netCredits - netDebits;
  }
  
  return 0;
}

/**
 * Determines if an account has a normal debit or credit balance
 */
export function getNormalBalance(accountType) {
  return ['asset', 'expense'].includes(accountType) ? 'debit' : 'credit';
}

/**
 * Formats currency for Philippine Peso
 */
export function formatCurrency(amount) {
  return new Intl.NumberFormat('en-PH', {
    style: 'currency',
    currency: 'PHP',
    minimumFractionDigits: 2
  }).format(amount);
}

/**
 * Calculates aging buckets for accounts payable
 */
export function calculateAging(dueDate, asOfDate = Date.now()) {
  const daysDiff = Math.floor((asOfDate - dueDate) / (1000 * 60 * 60 * 24));
  
  if (daysDiff < 0) return 'current';
  if (daysDiff <= 30) return '1-30';
  if (daysDiff <= 60) return '31-60';
  if (daysDiff <= 90) return '61-90';
  return 'over-90';
}

/**
 * Validates account code format
 */
export function validateAccountCode(code) {
  // Philippine COA typically uses 4-digit codes
  const pattern = /^\d{4}$/;
  return pattern.test(code);
}

/**
 * Determines account classification for financial statements
 */
export function getAccountClassification(accountType, subtype) {
  const classifications = {
    asset: {
      current_asset: 'Current Assets',
      non_current_asset: 'Non-Current Assets',
      default: 'Assets'
    },
    liability: {
      current_liability: 'Current Liabilities',
      non_current_liability: 'Non-Current Liabilities',
      default: 'Liabilities'
    },
    equity: {
      default: 'Equity'
    },
    revenue: {
      operating_revenue: 'Operating Revenue',
      other_revenue: 'Other Revenue',
      default: 'Revenue'
    },
    expense: {
      cost_of_sales: 'Cost of Sales',
      operating_expense: 'Operating Expenses',
      administrative_expense: 'Administrative Expenses',
      finance_cost: 'Finance Costs',
      other_expense: 'Other Expenses',
      default: 'Expenses'
    }
  };
  
  return classifications[accountType]?.[subtype] || classifications[accountType]?.default || 'Other';
}

/**
 * Rounds amount to 2 decimal places for currency
 */
export function roundCurrency(amount) {
  return Math.round((amount + Number.EPSILON) * 100) / 100;
}

/**
 * Validates date range for financial reports
 */
export function validateDateRange(startDate, endDate) {
  if (startDate && endDate && startDate > endDate) {
    throw new Error("Start date cannot be after end date");
  }
  
  if (endDate > Date.now()) {
    throw new Error("End date cannot be in the future");
  }
  
  return true;
}

/**
 * Gets fiscal year start date (assuming January 1st for Philippines)
 */
export function getFiscalYearStart(date) {
  const year = new Date(date).getFullYear();
  return new Date(year, 0, 1).getTime(); // January 1st
}

/**
 * Gets fiscal year end date (assuming December 31st for Philippines)
 */
export function getFiscalYearEnd(date) {
  const year = new Date(date).getFullYear();
  return new Date(year, 11, 31, 23, 59, 59, 999).getTime(); // December 31st
}

/**
 * BIR-compliant account code validation for Philippines
 */
export function validateBIRAccountCode(code, accountType) {
  // BIR Chart of Accounts structure for Philippines
  const birCodeRanges = {
    asset: {
      current: ['1000', '1999'],
      nonCurrent: ['2000', '2999']
    },
    liability: {
      current: ['3000', '3999'],
      nonCurrent: ['4000', '4999']
    },
    equity: ['5000', '5999'],
    revenue: ['6000', '6999'],
    expense: ['7000', '9999']
  };

  const codeNum = parseInt(code);

  if (accountType === 'asset') {
    return (codeNum >= 1000 && codeNum <= 2999);
  } else if (accountType === 'liability') {
    return (codeNum >= 3000 && codeNum <= 4999);
  } else if (accountType === 'equity') {
    return (codeNum >= 5000 && codeNum <= 5999);
  } else if (accountType === 'revenue') {
    return (codeNum >= 6000 && codeNum <= 6999);
  } else if (accountType === 'expense') {
    return (codeNum >= 7000 && codeNum <= 9999);
  }

  return false;
}

/**
 * Generate BIR-compliant books of accounts summary
 */
export function generateBIRBooksSummary(journalEntries, startDate, endDate) {
  const summary = {
    period: {
      startDate,
      endDate,
      description: `Books of Accounts for period ${new Date(startDate).toLocaleDateString()} to ${new Date(endDate).toLocaleDateString()}`
    },
    entries: {
      totalEntries: journalEntries.length,
      totalDebits: 0,
      totalCredits: 0,
      firstEntryNumber: null,
      lastEntryNumber: null
    },
    compliance: {
      isBIRCompliant: true,
      missingReferences: [],
      warnings: []
    }
  };

  journalEntries.forEach(entry => {
    summary.entries.totalDebits += entry.totalDebit;
    summary.entries.totalCredits += entry.totalCredit;

    if (!summary.entries.firstEntryNumber || entry.entryNumber < summary.entries.firstEntryNumber) {
      summary.entries.firstEntryNumber = entry.entryNumber;
    }

    if (!summary.entries.lastEntryNumber || entry.entryNumber > summary.entries.lastEntryNumber) {
      summary.entries.lastEntryNumber = entry.entryNumber;
    }

    // BIR compliance checks
    if (!entry.reference) {
      summary.compliance.missingReferences.push(entry.entryNumber);
    }

    if (entry.description.length < 10) {
      summary.compliance.warnings.push(`Entry ${entry.entryNumber} has insufficient description`);
    }
  });

  if (summary.compliance.missingReferences.length > 0) {
    summary.compliance.isBIRCompliant = false;
  }

  return summary;
}

/**
 * Calculate Philippine tax-related amounts
 */
export function calculatePhilippineTaxes(grossAmount, taxType = 'VAT') {
  const taxRates = {
    VAT: 0.12, // 12% VAT
    EWT: 0.02, // 2% Expanded Withholding Tax (common rate)
    PERCENTAGE_TAX: 0.03 // 3% Percentage Tax
  };

  const rate = taxRates[taxType] || 0;
  const taxAmount = roundCurrency(grossAmount * rate);
  const netAmount = roundCurrency(grossAmount - taxAmount);

  return {
    grossAmount: roundCurrency(grossAmount),
    taxRate: rate,
    taxAmount,
    netAmount,
    taxType
  };
}
