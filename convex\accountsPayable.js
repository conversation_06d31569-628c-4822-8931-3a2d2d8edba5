import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { calculateAging } from "./utils/accounting.js";

// Record a new payable (supplier invoice)
export const recordPayable = mutation({
  args: {
    vendorName: v.string(),
    vendorId: v.optional(v.string()),
    invoiceNumber: v.string(),
    invoiceDate: v.number(),
    dueDate: v.number(),
    amount: v.number(),
    terms: v.optional(v.string()),
    description: v.optional(v.string()),
    accountsPayableAccountId: v.id("accounts"), // AP account
    expenseAccountId: v.id("accounts"), // Expense or asset account
    reference: v.optional(v.string()),
    createdBy: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Validate amount
    if (args.amount <= 0) {
      throw new Error("Invoice amount must be greater than zero");
    }

    // Validate dates
    if (args.dueDate < args.invoiceDate) {
      throw new Error("Due date cannot be before invoice date");
    }

    // Check if invoice number already exists for this vendor
    const existingInvoice = await ctx.db
      .query("accounts_payable")
      .filter((q) => 
        q.and(
          q.eq(q.field("vendorName"), args.vendorName),
          q.eq(q.field("invoiceNumber"), args.invoiceNumber)
        )
      )
      .first();

    if (existingInvoice) {
      throw new Error(`Invoice ${args.invoiceNumber} already exists for vendor ${args.vendorName}`);
    }

    // Validate accounts exist and are active
    const apAccount = await ctx.db.get(args.accountsPayableAccountId);
    const expenseAccount = await ctx.db.get(args.expenseAccountId);

    if (!apAccount || !apAccount.isActive) {
      throw new Error("Accounts Payable account not found or inactive");
    }

    if (!expenseAccount || !expenseAccount.isActive) {
      throw new Error("Expense account not found or inactive");
    }

    // Validate account types
    if (apAccount.type !== 'liability') {
      throw new Error("Accounts Payable account must be a liability account");
    }

    const now = Date.now();

    // Create the payable record
    const payableId = await ctx.db.insert("accounts_payable", {
      vendorName: args.vendorName,
      vendorId: args.vendorId,
      invoiceNumber: args.invoiceNumber,
      invoiceDate: args.invoiceDate,
      dueDate: args.dueDate,
      originalAmount: args.amount,
      remainingBalance: args.amount,
      status: "open",
      terms: args.terms,
      description: args.description,
      createdAt: now,
      updatedAt: now,
    });

    // Create journal entry for the payable
    const journalEntryData = {
      date: args.invoiceDate,
      description: `Invoice ${args.invoiceNumber} from ${args.vendorName}`,
      reference: args.reference || args.invoiceNumber,
      entryType: "regular",
      lines: [
        {
          accountId: args.expenseAccountId,
          debit: args.amount,
          credit: 0,
          description: args.description || `Invoice ${args.invoiceNumber}`,
        },
        {
          accountId: args.accountsPayableAccountId,
          debit: 0,
          credit: args.amount,
          description: `AP - ${args.vendorName}`,
        },
      ],
      createdBy: args.createdBy,
    };

    // Import and call postJournalEntry
    const { postJournalEntry } = await import("./generalLedger.js");
    const journalResult = await postJournalEntry(ctx, journalEntryData);

    // Link the journal entry to the payable
    await ctx.db.patch(payableId, {
      journalEntryId: journalResult.journalEntryId,
    });

    return {
      payableId,
      journalEntryId: journalResult.journalEntryId,
      entryNumber: journalResult.entryNumber,
      warnings: journalResult.warnings,
    };
  },
});

// Apply payment to a payable
export const applyPaymentToPayable = mutation({
  args: {
    payableId: v.id("accounts_payable"),
    amount: v.number(),
    paymentDate: v.number(),
    paymentMethod: v.string(),
    reference: v.optional(v.string()),
    cashAccountId: v.id("accounts"), // Cash/Bank account
    accountsPayableAccountId: v.id("accounts"), // AP account
    createdBy: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Get the payable record
    const payable = await ctx.db.get(args.payableId);
    if (!payable) {
      throw new Error("Payable not found");
    }

    // Validate payment amount
    if (args.amount <= 0) {
      throw new Error("Payment amount must be greater than zero");
    }

    if (args.amount > payable.remainingBalance) {
      throw new Error(`Payment amount (${args.amount}) cannot exceed remaining balance (${payable.remainingBalance})`);
    }

    if (payable.status === "paid") {
      throw new Error("This payable has already been paid in full");
    }

    // Validate accounts
    const cashAccount = await ctx.db.get(args.cashAccountId);
    const apAccount = await ctx.db.get(args.accountsPayableAccountId);

    if (!cashAccount || !cashAccount.isActive) {
      throw new Error("Cash account not found or inactive");
    }

    if (!apAccount || !apAccount.isActive) {
      throw new Error("Accounts Payable account not found or inactive");
    }

    // Validate account types
    if (cashAccount.type !== 'asset') {
      throw new Error("Cash account must be an asset account");
    }

    if (apAccount.type !== 'liability') {
      throw new Error("Accounts Payable account must be a liability account");
    }

    const now = Date.now();

    // Create payment record
    const paymentId = await ctx.db.insert("ap_payments", {
      payableId: args.payableId,
      paymentDate: args.paymentDate,
      amount: args.amount,
      paymentMethod: args.paymentMethod,
      reference: args.reference,
      createdAt: now,
    });

    // Create journal entry for the payment
    const journalEntryData = {
      date: args.paymentDate,
      description: `Payment to ${payable.vendorName} for Invoice ${payable.invoiceNumber}`,
      reference: args.reference || `PAY-${payable.invoiceNumber}`,
      entryType: "regular",
      lines: [
        {
          accountId: args.accountsPayableAccountId,
          debit: args.amount,
          credit: 0,
          description: `Payment - ${payable.vendorName}`,
        },
        {
          accountId: args.cashAccountId,
          debit: 0,
          credit: args.amount,
          description: `Payment via ${args.paymentMethod}`,
        },
      ],
      createdBy: args.createdBy,
    };

    // Import and call postJournalEntry
    const { postJournalEntry } = await import("./generalLedger.js");
    const journalResult = await postJournalEntry(ctx, journalEntryData);

    // Update payment record with journal entry
    await ctx.db.patch(paymentId, {
      journalEntryId: journalResult.journalEntryId,
    });

    // Update payable balance and status
    const newBalance = payable.remainingBalance - args.amount;
    let newStatus = "open";
    
    if (newBalance === 0) {
      newStatus = "paid";
    } else if (newBalance < payable.originalAmount) {
      newStatus = "partial";
    }

    await ctx.db.patch(args.payableId, {
      remainingBalance: newBalance,
      status: newStatus,
      updatedAt: now,
    });

    return {
      paymentId,
      journalEntryId: journalResult.journalEntryId,
      entryNumber: journalResult.entryNumber,
      newBalance,
      newStatus,
      warnings: journalResult.warnings,
    };
  },
});

// Get aging schedule for accounts payable
export const getAgingSchedule = query({
  args: {
    asOfDate: v.optional(v.number()),
    vendorName: v.optional(v.string()),
    includeZeroBalances: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const asOfDate = args.asOfDate || Date.now();
    const includeZeroBalances = args.includeZeroBalances || false;

    // Get all payables
    let payables = await ctx.db.query("accounts_payable").collect();

    // Filter by vendor if specified
    if (args.vendorName) {
      payables = payables.filter(payable => 
        payable.vendorName.toLowerCase().includes(args.vendorName.toLowerCase())
      );
    }

    // Filter out zero balances if requested
    if (!includeZeroBalances) {
      payables = payables.filter(payable => payable.remainingBalance > 0);
    }

    // Calculate aging for each payable
    const agingData = payables.map(payable => {
      const agingBucket = calculateAging(payable.dueDate, asOfDate);
      const daysOverdue = Math.max(0, Math.floor((asOfDate - payable.dueDate) / (1000 * 60 * 60 * 24)));

      return {
        ...payable,
        agingBucket,
        daysOverdue,
        isOverdue: payable.dueDate < asOfDate && payable.remainingBalance > 0,
      };
    });

    // Group by aging buckets
    const agingBuckets = {
      current: agingData.filter(p => p.agingBucket === 'current'),
      '1-30': agingData.filter(p => p.agingBucket === '1-30'),
      '31-60': agingData.filter(p => p.agingBucket === '31-60'),
      '61-90': agingData.filter(p => p.agingBucket === '61-90'),
      'over-90': agingData.filter(p => p.agingBucket === 'over-90'),
    };

    // Calculate totals by bucket
    const bucketTotals = {
      current: agingBuckets.current.reduce((sum, p) => sum + p.remainingBalance, 0),
      '1-30': agingBuckets['1-30'].reduce((sum, p) => sum + p.remainingBalance, 0),
      '31-60': agingBuckets['31-60'].reduce((sum, p) => sum + p.remainingBalance, 0),
      '61-90': agingBuckets['61-90'].reduce((sum, p) => sum + p.remainingBalance, 0),
      'over-90': agingBuckets['over-90'].reduce((sum, p) => sum + p.remainingBalance, 0),
    };

    const totalOutstanding = Object.values(bucketTotals).reduce((sum, amount) => sum + amount, 0);
    const totalOverdue = bucketTotals['1-30'] + bucketTotals['31-60'] + bucketTotals['61-90'] + bucketTotals['over-90'];

    // Group by vendor
    const vendorSummary = {};
    agingData.forEach(payable => {
      if (!vendorSummary[payable.vendorName]) {
        vendorSummary[payable.vendorName] = {
          vendorName: payable.vendorName,
          totalOutstanding: 0,
          totalOverdue: 0,
          invoiceCount: 0,
          oldestInvoiceDate: payable.invoiceDate,
        };
      }

      const vendor = vendorSummary[payable.vendorName];
      vendor.totalOutstanding += payable.remainingBalance;
      vendor.invoiceCount += 1;
      
      if (payable.isOverdue) {
        vendor.totalOverdue += payable.remainingBalance;
      }

      if (payable.invoiceDate < vendor.oldestInvoiceDate) {
        vendor.oldestInvoiceDate = payable.invoiceDate;
      }
    });

    return {
      agingSchedule: agingData.sort((a, b) => a.dueDate - b.dueDate),
      agingBuckets,
      bucketTotals,
      vendorSummary: Object.values(vendorSummary).sort((a, b) => b.totalOutstanding - a.totalOutstanding),
      summary: {
        asOfDate,
        totalOutstanding,
        totalOverdue,
        totalPayables: agingData.length,
        totalVendors: Object.keys(vendorSummary).length,
        overduePercentage: totalOutstanding > 0 ? (totalOverdue / totalOutstanding) * 100 : 0,
      },
    };
  },
});

// Get payable details with payment history
export const getPayableDetails = query({
  args: {
    payableId: v.id("accounts_payable"),
  },
  handler: async (ctx, args) => {
    const payable = await ctx.db.get(args.payableId);
    if (!payable) {
      throw new Error("Payable not found");
    }

    // Get payment history
    const payments = await ctx.db
      .query("ap_payments")
      .withIndex("by_payable", (q) => q.eq("payableId", args.payableId))
      .collect();

    // Sort payments by date
    payments.sort((a, b) => a.paymentDate - b.paymentDate);

    // Get journal entry details if available
    let journalEntry = null;
    if (payable.journalEntryId) {
      journalEntry = await ctx.db.get(payable.journalEntryId);
    }

    // Calculate aging
    const agingBucket = calculateAging(payable.dueDate);
    const daysOverdue = Math.max(0, Math.floor((Date.now() - payable.dueDate) / (1000 * 60 * 60 * 24)));

    return {
      payable: {
        ...payable,
        agingBucket,
        daysOverdue,
        isOverdue: payable.dueDate < Date.now() && payable.remainingBalance > 0,
      },
      payments,
      journalEntry,
      summary: {
        totalPaid: payments.reduce((sum, payment) => sum + payment.amount, 0),
        paymentCount: payments.length,
        lastPaymentDate: payments.length > 0 ? payments[payments.length - 1].paymentDate : null,
      },
    };
  },
});

// Get vendor summary
export const getVendorSummary = query({
  args: {
    vendorName: v.optional(v.string()),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let payables = await ctx.db.query("accounts_payable").collect();

    // Filter by vendor if specified
    if (args.vendorName) {
      payables = payables.filter(payable => 
        payable.vendorName.toLowerCase().includes(args.vendorName.toLowerCase())
      );
    }

    // Filter by date range if specified
    if (args.startDate || args.endDate) {
      payables = payables.filter(payable => {
        if (args.startDate && payable.invoiceDate < args.startDate) return false;
        if (args.endDate && payable.invoiceDate > args.endDate) return false;
        return true;
      });
    }

    // Group by vendor
    const vendorData = {};
    
    payables.forEach(payable => {
      if (!vendorData[payable.vendorName]) {
        vendorData[payable.vendorName] = {
          vendorName: payable.vendorName,
          vendorId: payable.vendorId,
          totalInvoiced: 0,
          totalPaid: 0,
          totalOutstanding: 0,
          invoiceCount: 0,
          payables: [],
        };
      }

      const vendor = vendorData[payable.vendorName];
      vendor.totalInvoiced += payable.originalAmount;
      vendor.totalPaid += (payable.originalAmount - payable.remainingBalance);
      vendor.totalOutstanding += payable.remainingBalance;
      vendor.invoiceCount += 1;
      vendor.payables.push(payable);
    });

    // Convert to array and sort by total outstanding
    const vendorSummary = Object.values(vendorData).sort((a, b) => b.totalOutstanding - a.totalOutstanding);

    return {
      vendors: vendorSummary,
      summary: {
        totalVendors: vendorSummary.length,
        totalInvoiced: vendorSummary.reduce((sum, v) => sum + v.totalInvoiced, 0),
        totalPaid: vendorSummary.reduce((sum, v) => sum + v.totalPaid, 0),
        totalOutstanding: vendorSummary.reduce((sum, v) => sum + v.totalOutstanding, 0),
        totalInvoices: vendorSummary.reduce((sum, v) => sum + v.invoiceCount, 0),
        dateRange: {
          startDate: args.startDate,
          endDate: args.endDate,
        },
        generatedAt: Date.now(),
      },
    };
  },
});
