import { query } from "./_generated/server";
import { v } from "convex/values";
import { calculateAccountBalance, getAccountClassification, roundCurrency, validateDateRange } from "./utils/accounting.js";

// Generate Income Statement (Statement of Comprehensive Income)
export const generateIncomeStatement = query({
  args: {
    startDate: v.number(),
    endDate: v.number(),
    includeZeroBalances: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const includeZeroBalances = args.includeZeroBalances || false;

    // Get all active revenue and expense accounts
    const accounts = await ctx.db
      .query("accounts")
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    const incomeStatementAccounts = accounts.filter(account => 
      ['revenue', 'expense'].includes(account.type)
    );

    // Get all posted journal entries within date range
    let journalEntries = await ctx.db
      .query("journal_entries")
      .withIndex("by_status", (q) => q.eq("status", "posted"))
      .collect();

    journalEntries = journalEntries.filter(entry => {
      return entry.date >= args.startDate && entry.date <= args.endDate;
    });

    const journalEntryIds = journalEntries.map(entry => entry._id);

    // Get all journal entry lines for the filtered entries
    const allJournalLines = await ctx.db
      .query("journal_entry_lines")
      .collect();

    const relevantLines = allJournalLines.filter(line =>
      journalEntryIds.includes(line.journalEntryId)
    );

    // Calculate balances for income statement accounts
    const accountBalances = new Map();

    incomeStatementAccounts.forEach(account => {
      accountBalances.set(account._id, {
        account,
        totalDebits: 0,
        totalCredits: 0,
        balance: 0,
        classification: getAccountClassification(account.type, account.subtype),
      });
    });

    // Calculate totals
    relevantLines.forEach(line => {
      const accountData = accountBalances.get(line.accountId);
      if (accountData) {
        accountData.totalDebits += line.debit;
        accountData.totalCredits += line.credit;
      }
    });

    // Calculate final balances
    accountBalances.forEach((data, accountId) => {
      data.balance = calculateAccountBalance(
        data.account.type,
        0,
        data.totalDebits,
        data.totalCredits
      );
    });

    // Convert to array and filter zero balances if requested
    let incomeStatementData = Array.from(accountBalances.values());

    if (!includeZeroBalances) {
      incomeStatementData = incomeStatementData.filter(data => 
        Math.abs(data.balance) > 0.01
      );
    }

    // Organize by income statement sections
    const revenue = {
      operating: incomeStatementData.filter(data => 
        data.account.type === 'revenue' && 
        (data.account.subtype === 'operating_revenue' || !data.account.subtype)
      ).sort((a, b) => a.account.code.localeCompare(b.account.code)),
      
      other: incomeStatementData.filter(data => 
        data.account.type === 'revenue' && data.account.subtype === 'other_revenue'
      ).sort((a, b) => a.account.code.localeCompare(b.account.code)),
    };

    const expenses = {
      costOfSales: incomeStatementData.filter(data => 
        data.account.type === 'expense' && data.account.subtype === 'cost_of_sales'
      ).sort((a, b) => a.account.code.localeCompare(b.account.code)),
      
      operating: incomeStatementData.filter(data => 
        data.account.type === 'expense' && data.account.subtype === 'operating_expense'
      ).sort((a, b) => a.account.code.localeCompare(b.account.code)),
      
      administrative: incomeStatementData.filter(data => 
        data.account.type === 'expense' && data.account.subtype === 'administrative_expense'
      ).sort((a, b) => a.account.code.localeCompare(b.account.code)),
      
      finance: incomeStatementData.filter(data => 
        data.account.type === 'expense' && data.account.subtype === 'finance_cost'
      ).sort((a, b) => a.account.code.localeCompare(b.account.code)),
      
      other: incomeStatementData.filter(data => 
        data.account.type === 'expense' && 
        (data.account.subtype === 'other_expense' || 
         (!data.account.subtype && data.account.type === 'expense'))
      ).sort((a, b) => a.account.code.localeCompare(b.account.code)),
    };

    // Calculate totals and subtotals
    const totalOperatingRevenue = revenue.operating.reduce((sum, data) => sum + data.balance, 0);
    const totalOtherRevenue = revenue.other.reduce((sum, data) => sum + data.balance, 0);
    const totalRevenue = totalOperatingRevenue + totalOtherRevenue;

    const totalCostOfSales = expenses.costOfSales.reduce((sum, data) => sum + data.balance, 0);
    const grossProfit = totalOperatingRevenue - totalCostOfSales;

    const totalOperatingExpenses = expenses.operating.reduce((sum, data) => sum + data.balance, 0);
    const totalAdministrativeExpenses = expenses.administrative.reduce((sum, data) => sum + data.balance, 0);
    const operatingProfit = grossProfit - totalOperatingExpenses - totalAdministrativeExpenses;

    const totalFinanceCosts = expenses.finance.reduce((sum, data) => sum + data.balance, 0);
    const totalOtherExpenses = expenses.other.reduce((sum, data) => sum + data.balance, 0);
    const totalExpenses = totalCostOfSales + totalOperatingExpenses + totalAdministrativeExpenses + totalFinanceCosts + totalOtherExpenses;

    const profitBeforeTax = totalRevenue - totalExpenses;
    const netIncome = profitBeforeTax; // Tax calculation would be added here

    // Calculate margins and ratios
    const grossProfitMargin = totalOperatingRevenue > 0 ? (grossProfit / totalOperatingRevenue) * 100 : 0;
    const operatingProfitMargin = totalRevenue > 0 ? (operatingProfit / totalRevenue) * 100 : 0;
    const netProfitMargin = totalRevenue > 0 ? (netIncome / totalRevenue) * 100 : 0;

    return {
      incomeStatement: {
        revenue: {
          operating: revenue.operating,
          other: revenue.other,
          totalOperating: totalOperatingRevenue,
          totalOther: totalOtherRevenue,
          total: totalRevenue,
        },
        expenses: {
          costOfSales: expenses.costOfSales,
          operating: expenses.operating,
          administrative: expenses.administrative,
          finance: expenses.finance,
          other: expenses.other,
          totalCostOfSales,
          totalOperating: totalOperatingExpenses,
          totalAdministrative: totalAdministrativeExpenses,
          totalFinance: totalFinanceCosts,
          totalOther: totalOtherExpenses,
          total: totalExpenses,
        },
        profitLoss: {
          grossProfit,
          operatingProfit,
          profitBeforeTax,
          netIncome,
        },
        margins: {
          grossProfitMargin,
          operatingProfitMargin,
          netProfitMargin,
        },
      },
      summary: {
        startDate: args.startDate,
        endDate: args.endDate,
        generatedAt: Date.now(),
        totalAccounts: incomeStatementData.length,
        period: {
          days: Math.ceil((args.endDate - args.startDate) / (1000 * 60 * 60 * 24)),
        },
      },
      validation: {
        isValid: true,
        errors: [],
        warnings: netIncome < 0 ? ["Company shows a net loss for this period"] : [],
      },
    };
  },
});

// Generate comparative income statement
export const generateComparativeIncomeStatement = query({
  args: {
    currentStartDate: v.number(),
    currentEndDate: v.number(),
    priorStartDate: v.number(),
    priorEndDate: v.number(),
    includeZeroBalances: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Generate income statements for both periods
    const currentIncomeStatement = await generateIncomeStatement(ctx, {
      startDate: args.currentStartDate,
      endDate: args.currentEndDate,
      includeZeroBalances: args.includeZeroBalances,
    });

    const priorIncomeStatement = await generateIncomeStatement(ctx, {
      startDate: args.priorStartDate,
      endDate: args.priorEndDate,
      includeZeroBalances: args.includeZeroBalances,
    });

    // Calculate changes for revenue and expenses
    const calculateChanges = (currentSection, priorSection) => {
      const changes = currentSection.map(currentItem => {
        const priorItem = priorSection.find(p => p.account._id === currentItem.account._id);
        const priorBalance = priorItem ? priorItem.balance : 0;
        const change = currentItem.balance - priorBalance;
        const percentChange = priorBalance !== 0 ? (change / Math.abs(priorBalance)) * 100 : 0;

        return {
          ...currentItem,
          priorBalance,
          change,
          percentChange,
        };
      });

      // Add accounts that existed in prior but not current
      priorSection.forEach(priorItem => {
        if (!currentSection.find(c => c.account._id === priorItem.account._id)) {
          changes.push({
            ...priorItem,
            balance: 0,
            priorBalance: priorItem.balance,
            change: -priorItem.balance,
            percentChange: -100,
          });
        }
      });

      return changes.sort((a, b) => a.account.code.localeCompare(b.account.code));
    };

    const comparativeData = {
      revenue: {
        operating: calculateChanges(
          currentIncomeStatement.incomeStatement.revenue.operating,
          priorIncomeStatement.incomeStatement.revenue.operating
        ),
        other: calculateChanges(
          currentIncomeStatement.incomeStatement.revenue.other,
          priorIncomeStatement.incomeStatement.revenue.other
        ),
      },
      expenses: {
        costOfSales: calculateChanges(
          currentIncomeStatement.incomeStatement.expenses.costOfSales,
          priorIncomeStatement.incomeStatement.expenses.costOfSales
        ),
        operating: calculateChanges(
          currentIncomeStatement.incomeStatement.expenses.operating,
          priorIncomeStatement.incomeStatement.expenses.operating
        ),
        administrative: calculateChanges(
          currentIncomeStatement.incomeStatement.expenses.administrative,
          priorIncomeStatement.incomeStatement.expenses.administrative
        ),
        finance: calculateChanges(
          currentIncomeStatement.incomeStatement.expenses.finance,
          priorIncomeStatement.incomeStatement.expenses.finance
        ),
        other: calculateChanges(
          currentIncomeStatement.incomeStatement.expenses.other,
          priorIncomeStatement.incomeStatement.expenses.other
        ),
      },
    };

    // Calculate total changes
    const totalChanges = {
      revenue: currentIncomeStatement.incomeStatement.revenue.total - priorIncomeStatement.incomeStatement.revenue.total,
      expenses: currentIncomeStatement.incomeStatement.expenses.total - priorIncomeStatement.incomeStatement.expenses.total,
      grossProfit: currentIncomeStatement.incomeStatement.profitLoss.grossProfit - priorIncomeStatement.incomeStatement.profitLoss.grossProfit,
      operatingProfit: currentIncomeStatement.incomeStatement.profitLoss.operatingProfit - priorIncomeStatement.incomeStatement.profitLoss.operatingProfit,
      netIncome: currentIncomeStatement.incomeStatement.profitLoss.netIncome - priorIncomeStatement.incomeStatement.profitLoss.netIncome,
    };

    // Calculate percentage changes for key metrics
    const percentChanges = {
      revenue: priorIncomeStatement.incomeStatement.revenue.total !== 0 ? 
        (totalChanges.revenue / Math.abs(priorIncomeStatement.incomeStatement.revenue.total)) * 100 : 0,
      expenses: priorIncomeStatement.incomeStatement.expenses.total !== 0 ? 
        (totalChanges.expenses / Math.abs(priorIncomeStatement.incomeStatement.expenses.total)) * 100 : 0,
      netIncome: priorIncomeStatement.incomeStatement.profitLoss.netIncome !== 0 ? 
        (totalChanges.netIncome / Math.abs(priorIncomeStatement.incomeStatement.profitLoss.netIncome)) * 100 : 0,
    };

    return {
      current: currentIncomeStatement,
      prior: priorIncomeStatement,
      comparative: comparativeData,
      changes: totalChanges,
      percentChanges,
      summary: {
        currentPeriod: {
          startDate: args.currentStartDate,
          endDate: args.currentEndDate,
        },
        priorPeriod: {
          startDate: args.priorStartDate,
          endDate: args.priorEndDate,
        },
        generatedAt: Date.now(),
      },
    };
  },
});
