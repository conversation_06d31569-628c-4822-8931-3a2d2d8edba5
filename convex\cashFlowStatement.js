import { query } from "./_generated/server";
import { v } from "convex/values";
import { calculateAccountBalance } from "./utils/accounting.js";

// Generate Cash Flow Statement (Indirect Method)
export const generateCashFlowStatement = query({
  args: {
    startDate: v.number(),
    endDate: v.number(),
  },
  handler: async (ctx, args) => {
    // Get net income from income statement
    const incomeStatement = await ctx.db.query("functions").first(); // This would call generateIncomeStatement
    
    // For now, calculate net income directly
    const accounts = await ctx.db
      .query("accounts")
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    // Get all posted journal entries within date range
    let journalEntries = await ctx.db
      .query("journal_entries")
      .withIndex("by_status", (q) => q.eq("status", "posted"))
      .collect();

    journalEntries = journalEntries.filter(entry => {
      return entry.date >= args.startDate && entry.date <= args.endDate;
    });

    const journalEntryIds = journalEntries.map(entry => entry._id);

    // Get all journal entry lines for the filtered entries
    const allJournalLines = await ctx.db
      .query("journal_entry_lines")
      .collect();

    const relevantLines = allJournalLines.filter(line =>
      journalEntryIds.includes(line.journalEntryId)
    );

    // Calculate net income
    const revenueExpenseAccounts = accounts.filter(account => 
      ['revenue', 'expense'].includes(account.type)
    );

    let netIncome = 0;
    revenueExpenseAccounts.forEach(account => {
      const accountLines = relevantLines.filter(line => line.accountId === account._id);
      const totalDebits = accountLines.reduce((sum, line) => sum + line.debit, 0);
      const totalCredits = accountLines.reduce((sum, line) => sum + line.credit, 0);
      const accountBalance = calculateAccountBalance(account.type, 0, totalDebits, totalCredits);
      
      if (account.type === 'revenue') {
        netIncome += accountBalance;
      } else if (account.type === 'expense') {
        netIncome -= accountBalance;
      }
    });

    // Get cash and cash equivalent accounts
    const cashAccounts = accounts.filter(account => 
      account.type === 'asset' && (
        account.name.toLowerCase().includes('cash') ||
        account.name.toLowerCase().includes('bank') ||
        account.subtype === 'cash'
      )
    );

    // Calculate beginning and ending cash balances
    let beginningCashEntries = await ctx.db
      .query("journal_entries")
      .withIndex("by_status", (q) => q.eq("status", "posted"))
      .collect();

    beginningCashEntries = beginningCashEntries.filter(entry => entry.date < args.startDate);
    const beginningEntryIds = beginningCashEntries.map(entry => entry._id);

    const beginningLines = allJournalLines.filter(line =>
      beginningEntryIds.includes(line.journalEntryId)
    );

    let beginningCash = 0;
    let endingCash = 0;

    cashAccounts.forEach(account => {
      // Beginning cash
      const beginningAccountLines = beginningLines.filter(line => line.accountId === account._id);
      const beginningDebits = beginningAccountLines.reduce((sum, line) => sum + line.debit, 0);
      const beginningCredits = beginningAccountLines.reduce((sum, line) => sum + line.credit, 0);
      beginningCash += beginningDebits - beginningCredits;

      // Ending cash (beginning + period changes)
      const periodAccountLines = relevantLines.filter(line => line.accountId === account._id);
      const periodDebits = periodAccountLines.reduce((sum, line) => sum + line.debit, 0);
      const periodCredits = periodAccountLines.reduce((sum, line) => sum + line.credit, 0);
      endingCash += beginningDebits - beginningCredits + periodDebits - periodCredits;
    });

    // Analyze cash flows by category
    const operatingAdjustments = [];
    const investingActivities = [];
    const financingActivities = [];

    // Get accounts that typically represent non-cash items
    const nonCashAccounts = accounts.filter(account => 
      account.name.toLowerCase().includes('depreciation') ||
      account.name.toLowerCase().includes('amortization') ||
      account.name.toLowerCase().includes('allowance')
    );

    // Add back non-cash expenses
    nonCashAccounts.forEach(account => {
      const accountLines = relevantLines.filter(line => line.accountId === account._id);
      const totalDebits = accountLines.reduce((sum, line) => sum + line.debit, 0);
      const totalCredits = accountLines.reduce((sum, line) => sum + line.credit, 0);
      const netChange = totalDebits - totalCredits;

      if (Math.abs(netChange) > 0.01) {
        operatingAdjustments.push({
          description: `${account.name}`,
          amount: account.type === 'expense' ? netChange : -netChange, // Add back expenses
          account,
        });
      }
    });

    // Analyze working capital changes
    const workingCapitalAccounts = accounts.filter(account => 
      (account.type === 'asset' && account.subtype === 'current_asset' && !cashAccounts.includes(account)) ||
      (account.type === 'liability' && account.subtype === 'current_liability')
    );

    workingCapitalAccounts.forEach(account => {
      const accountLines = relevantLines.filter(line => line.accountId === account._id);
      const totalDebits = accountLines.reduce((sum, line) => sum + line.debit, 0);
      const totalCredits = accountLines.reduce((sum, line) => sum + line.credit, 0);
      const netChange = totalDebits - totalCredits;

      if (Math.abs(netChange) > 0.01) {
        let cashFlowEffect = 0;
        
        if (account.type === 'asset') {
          // Increase in current assets decreases cash flow
          cashFlowEffect = -netChange;
        } else if (account.type === 'liability') {
          // Increase in current liabilities increases cash flow
          cashFlowEffect = netChange;
        }

        operatingAdjustments.push({
          description: `Changes in ${account.name}`,
          amount: cashFlowEffect,
          account,
        });
      }
    });

    // Analyze investing activities (non-current assets)
    const investingAccounts = accounts.filter(account => 
      account.type === 'asset' && account.subtype === 'non_current_asset'
    );

    investingAccounts.forEach(account => {
      const accountLines = relevantLines.filter(line => line.accountId === account._id);
      const totalDebits = accountLines.reduce((sum, line) => sum + line.debit, 0);
      const totalCredits = accountLines.reduce((sum, line) => sum + line.credit, 0);
      const netChange = totalDebits - totalCredits;

      if (Math.abs(netChange) > 0.01) {
        investingActivities.push({
          description: netChange > 0 ? `Purchase of ${account.name}` : `Sale of ${account.name}`,
          amount: -netChange, // Purchase decreases cash, sale increases cash
          account,
        });
      }
    });

    // Analyze financing activities (equity and long-term debt)
    const financingAccounts = accounts.filter(account => 
      account.type === 'equity' ||
      (account.type === 'liability' && account.subtype === 'non_current_liability')
    );

    financingAccounts.forEach(account => {
      const accountLines = relevantLines.filter(line => line.accountId === account._id);
      const totalDebits = accountLines.reduce((sum, line) => sum + line.debit, 0);
      const totalCredits = accountLines.reduce((sum, line) => sum + line.credit, 0);
      const netChange = totalCredits - totalDebits; // For liabilities and equity

      if (Math.abs(netChange) > 0.01) {
        let description = '';
        if (account.type === 'equity') {
          description = netChange > 0 ? `Issuance of ${account.name}` : `Repurchase of ${account.name}`;
        } else {
          description = netChange > 0 ? `Proceeds from ${account.name}` : `Repayment of ${account.name}`;
        }

        financingActivities.push({
          description,
          amount: netChange,
          account,
        });
      }
    });

    // Calculate totals
    const totalOperatingAdjustments = operatingAdjustments.reduce((sum, item) => sum + item.amount, 0);
    const netCashFromOperating = netIncome + totalOperatingAdjustments;

    const totalInvestingCashFlow = investingActivities.reduce((sum, item) => sum + item.amount, 0);
    const totalFinancingCashFlow = financingActivities.reduce((sum, item) => sum + item.amount, 0);

    const netCashFlow = netCashFromOperating + totalInvestingCashFlow + totalFinancingCashFlow;
    const calculatedEndingCash = beginningCash + netCashFlow;

    // Validation
    const cashReconciles = Math.abs(endingCash - calculatedEndingCash) < 0.01;

    return {
      cashFlowStatement: {
        operatingActivities: {
          netIncome,
          adjustments: operatingAdjustments,
          totalAdjustments: totalOperatingAdjustments,
          netCashFromOperating,
        },
        investingActivities: {
          activities: investingActivities,
          netCashFromInvesting: totalInvestingCashFlow,
        },
        financingActivities: {
          activities: financingActivities,
          netCashFromFinancing: totalFinancingCashFlow,
        },
        cashSummary: {
          netCashFlow,
          beginningCash,
          endingCash: calculatedEndingCash,
          actualEndingCash: endingCash,
        },
      },
      summary: {
        startDate: args.startDate,
        endDate: args.endDate,
        generatedAt: Date.now(),
        method: 'indirect',
      },
      validation: {
        isValid: cashReconciles,
        errors: cashReconciles ? [] : [`Cash reconciliation failed. Calculated: ${calculatedEndingCash.toFixed(2)}, Actual: ${endingCash.toFixed(2)}`],
        warnings: [],
      },
    };
  },
});

// Generate comparative cash flow statement
export const generateComparativeCashFlowStatement = query({
  args: {
    currentStartDate: v.number(),
    currentEndDate: v.number(),
    priorStartDate: v.number(),
    priorEndDate: v.number(),
  },
  handler: async (ctx, args) => {
    // Generate cash flow statements for both periods
    const currentCashFlow = await generateCashFlowStatement(ctx, {
      startDate: args.currentStartDate,
      endDate: args.currentEndDate,
    });

    const priorCashFlow = await generateCashFlowStatement(ctx, {
      startDate: args.priorStartDate,
      endDate: args.priorEndDate,
    });

    // Calculate changes
    const changes = {
      netIncome: currentCashFlow.cashFlowStatement.operatingActivities.netIncome - 
                 priorCashFlow.cashFlowStatement.operatingActivities.netIncome,
      netCashFromOperating: currentCashFlow.cashFlowStatement.operatingActivities.netCashFromOperating - 
                           priorCashFlow.cashFlowStatement.operatingActivities.netCashFromOperating,
      netCashFromInvesting: currentCashFlow.cashFlowStatement.investingActivities.netCashFromInvesting - 
                           priorCashFlow.cashFlowStatement.investingActivities.netCashFromInvesting,
      netCashFromFinancing: currentCashFlow.cashFlowStatement.financingActivities.netCashFromFinancing - 
                           priorCashFlow.cashFlowStatement.financingActivities.netCashFromFinancing,
      netCashFlow: currentCashFlow.cashFlowStatement.cashSummary.netCashFlow - 
                   priorCashFlow.cashFlowStatement.cashSummary.netCashFlow,
    };

    // Calculate percentage changes
    const percentChanges = {
      netIncome: priorCashFlow.cashFlowStatement.operatingActivities.netIncome !== 0 ? 
        (changes.netIncome / Math.abs(priorCashFlow.cashFlowStatement.operatingActivities.netIncome)) * 100 : 0,
      netCashFromOperating: priorCashFlow.cashFlowStatement.operatingActivities.netCashFromOperating !== 0 ? 
        (changes.netCashFromOperating / Math.abs(priorCashFlow.cashFlowStatement.operatingActivities.netCashFromOperating)) * 100 : 0,
    };

    return {
      current: currentCashFlow,
      prior: priorCashFlow,
      changes,
      percentChanges,
      summary: {
        currentPeriod: {
          startDate: args.currentStartDate,
          endDate: args.currentEndDate,
        },
        priorPeriod: {
          startDate: args.priorStartDate,
          endDate: args.priorEndDate,
        },
        generatedAt: Date.now(),
      },
    };
  },
});

// Get cash flow by account analysis
export const getCashFlowByAccount = query({
  args: {
    startDate: v.number(),
    endDate: v.number(),
    accountId: v.optional(v.id("accounts")),
  },
  handler: async (ctx, args) => {
    // Get all cash accounts if no specific account provided
    let targetAccounts = [];
    
    if (args.accountId) {
      const account = await ctx.db.get(args.accountId);
      if (account) {
        targetAccounts = [account];
      }
    } else {
      const allAccounts = await ctx.db.query("accounts").collect();
      targetAccounts = allAccounts.filter(account => 
        account.type === 'asset' && (
          account.name.toLowerCase().includes('cash') ||
          account.name.toLowerCase().includes('bank')
        )
      );
    }

    const cashFlowDetails = [];

    for (const account of targetAccounts) {
      // Get journal entry lines for this account within date range
      let journalLines = await ctx.db
        .query("journal_entry_lines")
        .withIndex("by_account", (q) => q.eq("accountId", account._id))
        .collect();

      // Get corresponding journal entries and filter by date
      const journalEntryIds = [...new Set(journalLines.map(line => line.journalEntryId))];
      const journalEntries = await Promise.all(
        journalEntryIds.map(id => ctx.db.get(id))
      );

      const validEntryIds = journalEntries
        .filter(entry => {
          if (!entry || entry.status !== "posted") return false;
          return entry.date >= args.startDate && entry.date <= args.endDate;
        })
        .map(entry => entry._id);

      journalLines = journalLines.filter(line => 
        validEntryIds.includes(line.journalEntryId)
      );

      // Get detailed transaction information
      const transactions = await Promise.all(
        journalLines.map(async (line) => {
          const journalEntry = await ctx.db.get(line.journalEntryId);
          const allLinesInEntry = await ctx.db
            .query("journal_entry_lines")
            .withIndex("by_journal_entry", (q) => q.eq("journalEntryId", line.journalEntryId))
            .collect();

          // Determine cash flow classification
          let classification = 'operating'; // default
          
          const otherAccounts = await Promise.all(
            allLinesInEntry
              .filter(l => l.accountId !== account._id)
              .map(l => ctx.db.get(l.accountId))
          );

          // Simple classification logic
          if (otherAccounts.some(acc => acc && acc.subtype === 'non_current_asset')) {
            classification = 'investing';
          } else if (otherAccounts.some(acc => acc && (acc.type === 'equity' || acc.subtype === 'non_current_liability'))) {
            classification = 'financing';
          }

          return {
            ...line,
            journalEntry,
            classification,
            netCashEffect: line.debit - line.credit,
          };
        })
      );

      // Calculate totals by classification
      const operatingCashFlow = transactions
        .filter(t => t.classification === 'operating')
        .reduce((sum, t) => sum + t.netCashEffect, 0);

      const investingCashFlow = transactions
        .filter(t => t.classification === 'investing')
        .reduce((sum, t) => sum + t.netCashEffect, 0);

      const financingCashFlow = transactions
        .filter(t => t.classification === 'financing')
        .reduce((sum, t) => sum + t.netCashEffect, 0);

      cashFlowDetails.push({
        account,
        transactions,
        summary: {
          operatingCashFlow,
          investingCashFlow,
          financingCashFlow,
          totalCashFlow: operatingCashFlow + investingCashFlow + financingCashFlow,
          transactionCount: transactions.length,
        },
      });
    }

    return {
      cashFlowDetails,
      summary: {
        startDate: args.startDate,
        endDate: args.endDate,
        generatedAt: Date.now(),
        totalAccounts: cashFlowDetails.length,
      },
    };
  },
});
