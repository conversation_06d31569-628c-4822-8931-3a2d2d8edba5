import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Chart of Accounts
  accounts: defineTable({
    code: v.string(), // Account code (e.g., "1001", "2001")
    name: v.string(), // Account name
    type: v.union(
      v.literal("asset"),
      v.literal("liability"), 
      v.literal("equity"),
      v.literal("revenue"),
      v.literal("expense")
    ),
    subtype: v.optional(v.union(
      v.literal("current_asset"),
      v.literal("non_current_asset"),
      v.literal("current_liability"),
      v.literal("non_current_liability"),
      v.literal("operating_revenue"),
      v.literal("other_revenue"),
      v.literal("cost_of_sales"),
      v.literal("operating_expense"),
      v.literal("administrative_expense"),
      v.literal("finance_cost"),
      v.literal("other_expense")
    )),
    parentAccountId: v.optional(v.id("accounts")),
    isActive: v.boolean(),
    description: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_code", ["code"])
    .index("by_type", ["type"])
    .index("by_parent", ["parentAccountId"]),

  // Journal Entries
  journal_entries: defineTable({
    entryNumber: v.string(), // Sequential entry number
    date: v.number(), // Transaction date
    description: v.string(),
    reference: v.optional(v.string()), // External reference (invoice #, etc.)
    totalDebit: v.number(),
    totalCredit: v.number(),
    status: v.union(
      v.literal("draft"),
      v.literal("posted"),
      v.literal("reversed")
    ),
    entryType: v.union(
      v.literal("regular"),
      v.literal("adjusting"),
      v.literal("closing"),
      v.literal("reversing")
    ),
    reversalOf: v.optional(v.id("journal_entries")), // If this reverses another entry
    createdBy: v.optional(v.string()),
    createdAt: v.number(),
    postedAt: v.optional(v.number()),
  })
    .index("by_date", ["date"])
    .index("by_status", ["status"])
    .index("by_entry_number", ["entryNumber"]),

  // Journal Entry Line Items
  journal_entry_lines: defineTable({
    journalEntryId: v.id("journal_entries"),
    accountId: v.id("accounts"),
    debit: v.number(),
    credit: v.number(),
    description: v.optional(v.string()),
    lineNumber: v.number(), // Order within the entry
  })
    .index("by_journal_entry", ["journalEntryId"])
    .index("by_account", ["accountId"])
    .index("by_account_date", ["accountId", "journalEntryId"]),

  // Accounts Payable
  accounts_payable: defineTable({
    vendorName: v.string(),
    vendorId: v.optional(v.string()),
    invoiceNumber: v.string(),
    invoiceDate: v.number(),
    dueDate: v.number(),
    originalAmount: v.number(),
    remainingBalance: v.number(),
    status: v.union(
      v.literal("open"),
      v.literal("partial"),
      v.literal("paid"),
      v.literal("overdue")
    ),
    terms: v.optional(v.string()), // Payment terms
    description: v.optional(v.string()),
    journalEntryId: v.optional(v.id("journal_entries")), // Link to initial recording
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_vendor", ["vendorName"])
    .index("by_status", ["status"])
    .index("by_due_date", ["dueDate"]),

  // AP Payments
  ap_payments: defineTable({
    payableId: v.id("accounts_payable"),
    paymentDate: v.number(),
    amount: v.number(),
    paymentMethod: v.string(),
    reference: v.optional(v.string()), // Check number, etc.
    journalEntryId: v.optional(v.id("journal_entries")),
    createdAt: v.number(),
  })
    .index("by_payable", ["payableId"])
    .index("by_date", ["paymentDate"]),

  // Cash and Bank Accounts
  cash_bank_accounts: defineTable({
    accountId: v.id("accounts"), // Link to chart of accounts
    bankName: v.optional(v.string()),
    accountNumber: v.optional(v.string()),
    accountType: v.union(
      v.literal("cash"),
      v.literal("checking"),
      v.literal("savings"),
      v.literal("petty_cash")
    ),
    currentBalance: v.number(),
    reconciledBalance: v.optional(v.number()),
    lastReconciledDate: v.optional(v.number()),
    isActive: v.boolean(),
  })
    .index("by_account", ["accountId"]),

  // Report Snapshots
  report_snapshots: defineTable({
    reportType: v.union(
      v.literal("balance_sheet"),
      v.literal("income_statement"),
      v.literal("cash_flow"),
      v.literal("trial_balance")
    ),
    startDate: v.optional(v.number()),
    endDate: v.number(),
    data: v.any(), // JSON data of the report
    generatedAt: v.number(),
    generatedBy: v.optional(v.string()),
    parameters: v.optional(v.any()), // Additional parameters used
  })
    .index("by_type_date", ["reportType", "endDate"])
    .index("by_generated_at", ["generatedAt"]),

  // System Settings for Accounting
  accounting_settings: defineTable({
    key: v.string(),
    value: v.any(),
    description: v.optional(v.string()),
    updatedAt: v.number(),
  })
    .index("by_key", ["key"]),
});
