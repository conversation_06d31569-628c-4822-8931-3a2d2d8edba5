import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Save a financial report snapshot
export const snapshotFinancials = mutation({
  args: {
    reportType: v.union(
      v.literal("balance_sheet"),
      v.literal("income_statement"),
      v.literal("cash_flow"),
      v.literal("trial_balance")
    ),
    startDate: v.optional(v.number()),
    endDate: v.number(),
    data: v.any(), // The report data to snapshot
    parameters: v.optional(v.any()), // Additional parameters used to generate the report
    generatedBy: v.optional(v.string()),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Validate that we have data to snapshot
    if (!args.data) {
      throw new Error("Report data is required for snapshot");
    }

    // Validate date range for period reports
    if (["income_statement", "cash_flow"].includes(args.reportType) && !args.startDate) {
      throw new Error(`Start date is required for ${args.reportType} snapshots`);
    }

    const now = Date.now();

    // Create the snapshot
    const snapshotId = await ctx.db.insert("report_snapshots", {
      reportType: args.reportType,
      startDate: args.startDate,
      endDate: args.endDate,
      data: args.data,
      parameters: args.parameters,
      generatedAt: now,
      generatedBy: args.generatedBy,
    });

    // Add metadata to the snapshot data
    const enhancedData = {
      ...args.data,
      snapshot: {
        id: snapshotId,
        reportType: args.reportType,
        startDate: args.startDate,
        endDate: args.endDate,
        generatedAt: now,
        generatedBy: args.generatedBy,
        description: args.description,
      },
    };

    // Update the snapshot with enhanced data
    await ctx.db.patch(snapshotId, {
      data: enhancedData,
    });

    return {
      snapshotId,
      reportType: args.reportType,
      generatedAt: now,
      message: "Financial report snapshot created successfully",
    };
  },
});

// Generate and snapshot balance sheet
export const snapshotBalanceSheet = mutation({
  args: {
    asOfDate: v.number(),
    includeZeroBalances: v.optional(v.boolean()),
    generatedBy: v.optional(v.string()),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Import and generate balance sheet
    const { generateBalanceSheet } = await import("./balanceSheet.js");
    const balanceSheetData = await generateBalanceSheet(ctx, {
      asOfDate: args.asOfDate,
      includeZeroBalances: args.includeZeroBalances,
    });

    // Create snapshot
    return await snapshotFinancials(ctx, {
      reportType: "balance_sheet",
      endDate: args.asOfDate,
      data: balanceSheetData,
      parameters: {
        asOfDate: args.asOfDate,
        includeZeroBalances: args.includeZeroBalances,
      },
      generatedBy: args.generatedBy,
      description: args.description,
    });
  },
});

// Generate and snapshot income statement
export const snapshotIncomeStatement = mutation({
  args: {
    startDate: v.number(),
    endDate: v.number(),
    includeZeroBalances: v.optional(v.boolean()),
    generatedBy: v.optional(v.string()),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Import and generate income statement
    const { generateIncomeStatement } = await import("./incomeStatement.js");
    const incomeStatementData = await generateIncomeStatement(ctx, {
      startDate: args.startDate,
      endDate: args.endDate,
      includeZeroBalances: args.includeZeroBalances,
    });

    // Create snapshot
    return await snapshotFinancials(ctx, {
      reportType: "income_statement",
      startDate: args.startDate,
      endDate: args.endDate,
      data: incomeStatementData,
      parameters: {
        startDate: args.startDate,
        endDate: args.endDate,
        includeZeroBalances: args.includeZeroBalances,
      },
      generatedBy: args.generatedBy,
      description: args.description,
    });
  },
});

// Generate and snapshot cash flow statement
export const snapshotCashFlowStatement = mutation({
  args: {
    startDate: v.number(),
    endDate: v.number(),
    generatedBy: v.optional(v.string()),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Import and generate cash flow statement
    const { generateCashFlowStatement } = await import("./cashFlowStatement.js");
    const cashFlowData = await generateCashFlowStatement(ctx, {
      startDate: args.startDate,
      endDate: args.endDate,
    });

    // Create snapshot
    return await snapshotFinancials(ctx, {
      reportType: "cash_flow",
      startDate: args.startDate,
      endDate: args.endDate,
      data: cashFlowData,
      parameters: {
        startDate: args.startDate,
        endDate: args.endDate,
      },
      generatedBy: args.generatedBy,
      description: args.description,
    });
  },
});

// Generate and snapshot trial balance
export const snapshotTrialBalance = mutation({
  args: {
    startDate: v.optional(v.number()),
    endDate: v.number(),
    includeZeroBalances: v.optional(v.boolean()),
    generatedBy: v.optional(v.string()),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Import and generate trial balance
    const { generateTrialBalance } = await import("./trialBalance.js");
    const trialBalanceData = await generateTrialBalance(ctx, {
      startDate: args.startDate,
      endDate: args.endDate,
      includeZeroBalances: args.includeZeroBalances,
    });

    // Create snapshot
    return await snapshotFinancials(ctx, {
      reportType: "trial_balance",
      startDate: args.startDate,
      endDate: args.endDate,
      data: trialBalanceData,
      parameters: {
        startDate: args.startDate,
        endDate: args.endDate,
        includeZeroBalances: args.includeZeroBalances,
      },
      generatedBy: args.generatedBy,
      description: args.description,
    });
  },
});

// Get report snapshots with filtering
export const getReportSnapshots = query({
  args: {
    reportType: v.optional(v.union(
      v.literal("balance_sheet"),
      v.literal("income_statement"),
      v.literal("cash_flow"),
      v.literal("trial_balance")
    )),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let snapshots = await ctx.db.query("report_snapshots").collect();

    // Filter by report type
    if (args.reportType) {
      snapshots = snapshots.filter(snapshot => snapshot.reportType === args.reportType);
    }

    // Filter by date range
    if (args.startDate || args.endDate) {
      snapshots = snapshots.filter(snapshot => {
        if (args.startDate && snapshot.endDate < args.startDate) return false;
        if (args.endDate && snapshot.endDate > args.endDate) return false;
        return true;
      });
    }

    // Sort by generation date (newest first)
    snapshots.sort((a, b) => b.generatedAt - a.generatedAt);

    // Apply limit
    if (args.limit && args.limit > 0) {
      snapshots = snapshots.slice(0, args.limit);
    }

    return {
      snapshots,
      summary: {
        totalSnapshots: snapshots.length,
        reportTypes: [...new Set(snapshots.map(s => s.reportType))],
        dateRange: {
          earliest: snapshots.length > 0 ? Math.min(...snapshots.map(s => s.endDate)) : null,
          latest: snapshots.length > 0 ? Math.max(...snapshots.map(s => s.endDate)) : null,
        },
      },
    };
  },
});

// Get a specific report snapshot
export const getReportSnapshot = query({
  args: {
    snapshotId: v.id("report_snapshots"),
  },
  handler: async (ctx, args) => {
    const snapshot = await ctx.db.get(args.snapshotId);
    if (!snapshot) {
      throw new Error("Report snapshot not found");
    }

    return snapshot;
  },
});

// Compare two report snapshots
export const compareReportSnapshots = query({
  args: {
    currentSnapshotId: v.id("report_snapshots"),
    priorSnapshotId: v.id("report_snapshots"),
  },
  handler: async (ctx, args) => {
    const currentSnapshot = await ctx.db.get(args.currentSnapshotId);
    const priorSnapshot = await ctx.db.get(args.priorSnapshotId);

    if (!currentSnapshot) {
      throw new Error("Current snapshot not found");
    }

    if (!priorSnapshot) {
      throw new Error("Prior snapshot not found");
    }

    if (currentSnapshot.reportType !== priorSnapshot.reportType) {
      throw new Error("Cannot compare snapshots of different report types");
    }

    // Basic comparison structure
    const comparison = {
      reportType: currentSnapshot.reportType,
      current: {
        snapshot: currentSnapshot,
        period: {
          startDate: currentSnapshot.startDate,
          endDate: currentSnapshot.endDate,
        },
      },
      prior: {
        snapshot: priorSnapshot,
        period: {
          startDate: priorSnapshot.startDate,
          endDate: priorSnapshot.endDate,
        },
      },
      comparison: {
        generatedAt: Date.now(),
        timeDifference: currentSnapshot.generatedAt - priorSnapshot.generatedAt,
      },
    };

    // Add report-specific comparison logic
    if (currentSnapshot.reportType === "balance_sheet") {
      // Compare balance sheet totals
      const currentTotals = currentSnapshot.data?.balanceSheet?.totals || {};
      const priorTotals = priorSnapshot.data?.balanceSheet?.totals || {};

      comparison.comparison.changes = {
        assets: (currentTotals.assets || 0) - (priorTotals.assets || 0),
        liabilities: (currentTotals.liabilities || 0) - (priorTotals.liabilities || 0),
        equity: (currentTotals.equity || 0) - (priorTotals.equity || 0),
      };
    } else if (currentSnapshot.reportType === "income_statement") {
      // Compare income statement totals
      const currentProfitLoss = currentSnapshot.data?.incomeStatement?.profitLoss || {};
      const priorProfitLoss = priorSnapshot.data?.incomeStatement?.profitLoss || {};

      comparison.comparison.changes = {
        revenue: (currentSnapshot.data?.incomeStatement?.revenue?.total || 0) - 
                (priorSnapshot.data?.incomeStatement?.revenue?.total || 0),
        expenses: (currentSnapshot.data?.incomeStatement?.expenses?.total || 0) - 
                 (priorSnapshot.data?.incomeStatement?.expenses?.total || 0),
        netIncome: (currentProfitLoss.netIncome || 0) - (priorProfitLoss.netIncome || 0),
      };
    }

    return comparison;
  },
});

// Delete old snapshots (cleanup utility)
export const deleteOldSnapshots = mutation({
  args: {
    olderThanDays: v.number(),
    reportType: v.optional(v.union(
      v.literal("balance_sheet"),
      v.literal("income_statement"),
      v.literal("cash_flow"),
      v.literal("trial_balance")
    )),
    keepMinimum: v.optional(v.number()), // Minimum number to keep regardless of age
  },
  handler: async (ctx, args) => {
    const cutoffDate = Date.now() - (args.olderThanDays * 24 * 60 * 60 * 1000);
    const keepMinimum = args.keepMinimum || 5;

    let snapshots = await ctx.db.query("report_snapshots").collect();

    // Filter by report type if specified
    if (args.reportType) {
      snapshots = snapshots.filter(snapshot => snapshot.reportType === args.reportType);
    }

    // Sort by generation date (newest first)
    snapshots.sort((a, b) => b.generatedAt - a.generatedAt);

    // Identify snapshots to delete
    const snapshotsToDelete = snapshots
      .slice(keepMinimum) // Keep minimum number
      .filter(snapshot => snapshot.generatedAt < cutoffDate);

    // Delete the snapshots
    const deletePromises = snapshotsToDelete.map(snapshot => 
      ctx.db.delete(snapshot._id)
    );

    await Promise.all(deletePromises);

    return {
      deletedCount: snapshotsToDelete.length,
      remainingCount: snapshots.length - snapshotsToDelete.length,
      cutoffDate,
      message: `Deleted ${snapshotsToDelete.length} old snapshots`,
    };
  },
});

// Get snapshot statistics
export const getSnapshotStatistics = query({
  args: {},
  handler: async (ctx, args) => {
    const snapshots = await ctx.db.query("report_snapshots").collect();

    // Group by report type
    const byReportType = {};
    snapshots.forEach(snapshot => {
      if (!byReportType[snapshot.reportType]) {
        byReportType[snapshot.reportType] = {
          count: 0,
          oldest: snapshot.generatedAt,
          newest: snapshot.generatedAt,
        };
      }

      const typeData = byReportType[snapshot.reportType];
      typeData.count++;
      typeData.oldest = Math.min(typeData.oldest, snapshot.generatedAt);
      typeData.newest = Math.max(typeData.newest, snapshot.generatedAt);
    });

    // Calculate storage usage (approximate)
    const totalDataSize = snapshots.reduce((sum, snapshot) => {
      return sum + JSON.stringify(snapshot.data).length;
    }, 0);

    return {
      statistics: {
        totalSnapshots: snapshots.length,
        byReportType,
        dateRange: {
          oldest: snapshots.length > 0 ? Math.min(...snapshots.map(s => s.generatedAt)) : null,
          newest: snapshots.length > 0 ? Math.max(...snapshots.map(s => s.generatedAt)) : null,
        },
        approximateDataSize: totalDataSize,
        averageSnapshotSize: snapshots.length > 0 ? totalDataSize / snapshots.length : 0,
      },
      generatedAt: Date.now(),
    };
  },
});
