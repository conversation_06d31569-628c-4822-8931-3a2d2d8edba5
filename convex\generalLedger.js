import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { validateDoubleEntry, generateEntryNumber, calculateAccountBalance } from "./utils/accounting.js";
import { validatePFRSCompliance, validateBIRAuditTrail } from "./utils/pfrsValidation.js";

// Post a journal entry with full validation
export const postJournalEntry = mutation({
  args: {
    date: v.number(),
    description: v.string(),
    reference: v.optional(v.string()),
    entryType: v.optional(v.union(
      v.literal("regular"),
      v.literal("adjusting"),
      v.literal("closing"),
      v.literal("reversing")
    )),
    lines: v.array(v.object({
      accountId: v.id("accounts"),
      debit: v.number(),
      credit: v.number(),
      description: v.optional(v.string()),
    })),
    createdBy: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Validate double-entry accounting
    const doubleEntryValidation = validateDoubleEntry(args.lines);
    if (!doubleEntryValidation.isValid) {
      throw new Error(
        `Journal entry is not balanced. Debits: ${doubleEntryValidation.totalDebits}, Credits: ${doubleEntryValidation.totalCredits}`
      );
    }

    // Validate that all accounts exist and are active
    const accounts = await Promise.all(
      args.lines.map(line => ctx.db.get(line.accountId))
    );

    for (let i = 0; i < accounts.length; i++) {
      const account = accounts[i];
      if (!account) {
        throw new Error(`Account not found for line ${i + 1}`);
      }
      if (!account.isActive) {
        throw new Error(`Account ${account.name} is inactive`);
      }
    }

    // Validate each line has either debit or credit (not both, not neither)
    for (let i = 0; i < args.lines.length; i++) {
      const line = args.lines[i];
      if ((line.debit > 0 && line.credit > 0) || (line.debit === 0 && line.credit === 0)) {
        throw new Error(`Line ${i + 1} must have either debit or credit amount, not both or neither`);
      }
      if (line.debit < 0 || line.credit < 0) {
        throw new Error(`Line ${i + 1} cannot have negative amounts`);
      }
    }

    // PFRS compliance validation
    const pfrsValidation = validatePFRSCompliance({ lines: args.lines }, accounts);
    
    // BIR audit trail validation
    const birValidation = validateBIRAuditTrail({
      reference: args.reference,
      description: args.description,
      totalDebit: doubleEntryValidation.totalDebits,
      totalCredit: doubleEntryValidation.totalCredits,
    });

    // Generate entry number
    const lastEntry = await ctx.db
      .query("journal_entries")
      .withIndex("by_entry_number")
      .order("desc")
      .first();
    
    const entryNumber = generateEntryNumber(lastEntry?.entryNumber);

    const now = Date.now();

    // Create journal entry
    const journalEntryId = await ctx.db.insert("journal_entries", {
      entryNumber,
      date: args.date,
      description: args.description,
      reference: args.reference,
      totalDebit: doubleEntryValidation.totalDebits,
      totalCredit: doubleEntryValidation.totalCredits,
      status: "posted",
      entryType: args.entryType || "regular",
      createdBy: args.createdBy,
      createdAt: now,
      postedAt: now,
    });

    // Create journal entry lines
    const linePromises = args.lines.map((line, index) =>
      ctx.db.insert("journal_entry_lines", {
        journalEntryId,
        accountId: line.accountId,
        debit: line.debit,
        credit: line.credit,
        description: line.description,
        lineNumber: index + 1,
      })
    );

    await Promise.all(linePromises);

    // Return the created entry with validation results
    return {
      journalEntryId,
      entryNumber,
      pfrsValidation,
      birValidation,
      warnings: [
        ...pfrsValidation.warnings,
        ...birValidation.warnings,
      ],
      suggestions: pfrsValidation.suggestions || [],
    };
  },
});

// Get ledger entries for a specific account
export const getLedgerByAccount = query({
  args: {
    accountId: v.id("accounts"),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    includeRunningBalance: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const account = await ctx.db.get(args.accountId);
    if (!account) {
      throw new Error("Account not found");
    }

    // Get all journal entry lines for this account
    let journalLines = await ctx.db
      .query("journal_entry_lines")
      .withIndex("by_account", (q) => q.eq("accountId", args.accountId))
      .collect();

    // Get corresponding journal entries and filter by date/status
    const journalEntryIds = [...new Set(journalLines.map(line => line.journalEntryId))];
    const journalEntries = await Promise.all(
      journalEntryIds.map(id => ctx.db.get(id))
    );

    const journalEntryMap = new Map();
    journalEntries.forEach(entry => {
      if (entry && entry.status === "posted") {
        if (!args.startDate || entry.date >= args.startDate) {
          if (!args.endDate || entry.date <= args.endDate) {
            journalEntryMap.set(entry._id, entry);
          }
        }
      }
    });

    // Filter lines to only include those from valid journal entries
    journalLines = journalLines.filter(line => 
      journalEntryMap.has(line.journalEntryId)
    );

    // Sort by date and entry number
    journalLines.sort((a, b) => {
      const entryA = journalEntryMap.get(a.journalEntryId);
      const entryB = journalEntryMap.get(b.journalEntryId);
      
      if (entryA.date !== entryB.date) {
        return entryA.date - entryB.date;
      }
      return entryA.entryNumber.localeCompare(entryB.entryNumber);
    });

    // Calculate running balance if requested
    let runningBalance = 0;
    const ledgerEntries = journalLines.map(line => {
      const journalEntry = journalEntryMap.get(line.journalEntryId);
      
      if (args.includeRunningBalance) {
        // Calculate running balance based on account type
        if (account.type === 'asset' || account.type === 'expense') {
          runningBalance += line.debit - line.credit;
        } else {
          runningBalance += line.credit - line.debit;
        }
      }

      return {
        ...line,
        journalEntry: {
          entryNumber: journalEntry.entryNumber,
          date: journalEntry.date,
          description: journalEntry.description,
          reference: journalEntry.reference,
        },
        runningBalance: args.includeRunningBalance ? runningBalance : undefined,
      };
    });

    return {
      account,
      entries: ledgerEntries,
      totalDebits: journalLines.reduce((sum, line) => sum + line.debit, 0),
      totalCredits: journalLines.reduce((sum, line) => sum + line.credit, 0),
      finalBalance: runningBalance,
    };
  },
});

// Get general ledger for all accounts
export const getGeneralLedger = query({
  args: {
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    accountTypes: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    // Get all accounts
    let accounts = await ctx.db.query("accounts").collect();
    
    if (args.accountTypes && args.accountTypes.length > 0) {
      accounts = accounts.filter(account => 
        args.accountTypes.includes(account.type)
      );
    }

    // Get ledger for each account
    const ledgerPromises = accounts.map(async (account) => {
      const ledgerData = await getLedgerByAccount(ctx, {
        accountId: account._id,
        startDate: args.startDate,
        endDate: args.endDate,
        includeRunningBalance: true,
      });
      
      return {
        account,
        ...ledgerData,
      };
    });

    const ledgers = await Promise.all(ledgerPromises);

    // Filter out accounts with no transactions
    const activeLedgers = ledgers.filter(ledger => 
      ledger.entries && ledger.entries.length > 0
    );

    // Group by account type
    const groupedLedgers = {
      asset: activeLedgers.filter(l => l.account.type === 'asset'),
      liability: activeLedgers.filter(l => l.account.type === 'liability'),
      equity: activeLedgers.filter(l => l.account.type === 'equity'),
      revenue: activeLedgers.filter(l => l.account.type === 'revenue'),
      expense: activeLedgers.filter(l => l.account.type === 'expense'),
    };

    return {
      ledgers: activeLedgers,
      groupedLedgers,
      summary: {
        totalAccounts: activeLedgers.length,
        totalTransactions: activeLedgers.reduce((sum, l) => sum + l.entries.length, 0),
        dateRange: {
          startDate: args.startDate,
          endDate: args.endDate,
        },
      },
    };
  },
});

// Reverse a journal entry
export const reverseJournalEntry = mutation({
  args: {
    originalEntryId: v.id("journal_entries"),
    reversalDate: v.number(),
    description: v.string(),
    createdBy: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const originalEntry = await ctx.db.get(args.originalEntryId);
    if (!originalEntry) {
      throw new Error("Original journal entry not found");
    }

    if (originalEntry.status !== "posted") {
      throw new Error("Can only reverse posted journal entries");
    }

    // Get original entry lines
    const originalLines = await ctx.db
      .query("journal_entry_lines")
      .withIndex("by_journal_entry", (q) => q.eq("journalEntryId", args.originalEntryId))
      .collect();

    // Create reversed lines (swap debits and credits)
    const reversedLines = originalLines.map(line => ({
      accountId: line.accountId,
      debit: line.credit,
      credit: line.debit,
      description: `Reversal of ${originalEntry.entryNumber} - ${line.description || ''}`,
    }));

    // Post the reversal entry
    const reversalResult = await postJournalEntry(ctx, {
      date: args.reversalDate,
      description: args.description,
      reference: `REV-${originalEntry.entryNumber}`,
      entryType: "reversing",
      lines: reversedLines,
      createdBy: args.createdBy,
    });

    // Update original entry status
    await ctx.db.patch(args.originalEntryId, {
      status: "reversed",
      updatedAt: Date.now(),
    });

    // Update reversal entry to link back to original
    await ctx.db.patch(reversalResult.journalEntryId, {
      reversalOf: args.originalEntryId,
    });

    return {
      originalEntryId: args.originalEntryId,
      reversalEntryId: reversalResult.journalEntryId,
      reversalEntryNumber: reversalResult.entryNumber,
    };
  },
});

// Get journal entry details
export const getJournalEntry = query({
  args: {
    entryId: v.id("journal_entries"),
  },
  handler: async (ctx, args) => {
    const entry = await ctx.db.get(args.entryId);
    if (!entry) {
      throw new Error("Journal entry not found");
    }

    const lines = await ctx.db
      .query("journal_entry_lines")
      .withIndex("by_journal_entry", (q) => q.eq("journalEntryId", args.entryId))
      .collect();

    // Get account information for each line
    const linesWithAccounts = await Promise.all(
      lines.map(async (line) => {
        const account = await ctx.db.get(line.accountId);
        return {
          ...line,
          account,
        };
      })
    );

    // Sort lines by line number
    linesWithAccounts.sort((a, b) => a.lineNumber - b.lineNumber);

    return {
      ...entry,
      lines: linesWithAccounts,
    };
  },
});
