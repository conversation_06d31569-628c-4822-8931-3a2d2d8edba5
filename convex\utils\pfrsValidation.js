// PFRS (Philippine Financial Reporting Standards) validation helpers

/**
 * PFRS 15 - Revenue Recognition validation
 * Ensures revenue is recognized when performance obligations are satisfied
 */
export function validateRevenueRecognition(entry) {
  const revenueLines = entry.lines.filter(line => 
    line.account?.type === 'revenue' && line.credit > 0
  );
  
  if (revenueLines.length === 0) return { isValid: true };
  
  const validationResults = {
    isValid: true,
    warnings: [],
    errors: []
  };
  
  // Check for proper revenue recognition criteria
  revenueLines.forEach(line => {
    // Revenue should have corresponding asset (cash/AR) or liability reduction
    const hasCorrespondingEntry = entry.lines.some(otherLine => 
      otherLine !== line && (
        (otherLine.account?.type === 'asset' && otherLine.debit > 0) ||
        (otherLine.account?.type === 'liability' && otherLine.debit > 0)
      )
    );
    
    if (!hasCorrespondingEntry) {
      validationResults.warnings.push(
        `Revenue recognition for ${line.account?.name} should have corresponding asset increase or liability decrease`
      );
    }
  });
  
  return validationResults;
}

/**
 * PAS 1 - Presentation of Financial Statements validation
 * Ensures proper classification and presentation
 */
export function validateFinancialStatementPresentation(accounts) {
  const validationResults = {
    isValid: true,
    warnings: [],
    errors: []
  };
  
  // Check for required account types
  const requiredTypes = ['asset', 'liability', 'equity', 'revenue', 'expense'];
  const presentTypes = [...new Set(accounts.map(acc => acc.type))];
  
  requiredTypes.forEach(type => {
    if (!presentTypes.includes(type)) {
      validationResults.warnings.push(
        `Missing accounts of type: ${type}. Consider adding accounts for complete financial statement presentation.`
      );
    }
  });
  
  // Check for proper current/non-current classification
  const assets = accounts.filter(acc => acc.type === 'asset');
  const liabilities = accounts.filter(acc => acc.type === 'liability');
  
  const hasCurrentAssets = assets.some(acc => acc.subtype === 'current_asset');
  const hasNonCurrentAssets = assets.some(acc => acc.subtype === 'non_current_asset');
  const hasCurrentLiabilities = liabilities.some(acc => acc.subtype === 'current_liability');
  const hasNonCurrentLiabilities = liabilities.some(acc => acc.subtype === 'non_current_liability');
  
  if (assets.length > 0 && !hasCurrentAssets) {
    validationResults.warnings.push('Consider classifying assets as current or non-current for PAS 1 compliance');
  }
  
  if (liabilities.length > 0 && !hasCurrentLiabilities) {
    validationResults.warnings.push('Consider classifying liabilities as current or non-current for PAS 1 compliance');
  }
  
  return validationResults;
}

/**
 * PAS 7 - Cash Flow Statement validation
 * Ensures proper classification of cash flows
 */
export function validateCashFlowClassification(entry) {
  const validationResults = {
    isValid: true,
    warnings: [],
    errors: [],
    suggestedClassification: null
  };
  
  const cashAccounts = entry.lines.filter(line => 
    line.account?.subtype === 'cash' || 
    line.account?.name?.toLowerCase().includes('cash') ||
    line.account?.name?.toLowerCase().includes('bank')
  );
  
  if (cashAccounts.length === 0) {
    return validationResults; // Not a cash transaction
  }
  
  // Suggest classification based on other accounts involved
  const otherAccounts = entry.lines.filter(line => !cashAccounts.includes(line));
  
  if (otherAccounts.some(line => line.account?.type === 'revenue' || line.account?.type === 'expense')) {
    validationResults.suggestedClassification = 'operating';
  } else if (otherAccounts.some(line => 
    line.account?.subtype === 'non_current_asset' || 
    line.account?.name?.toLowerCase().includes('equipment') ||
    line.account?.name?.toLowerCase().includes('property')
  )) {
    validationResults.suggestedClassification = 'investing';
  } else if (otherAccounts.some(line => 
    line.account?.type === 'equity' ||
    line.account?.name?.toLowerCase().includes('loan') ||
    line.account?.name?.toLowerCase().includes('debt')
  )) {
    validationResults.suggestedClassification = 'financing';
  }
  
  return validationResults;
}

/**
 * PAS 16 - Property, Plant and Equipment validation
 * Basic validation for asset recognition and depreciation
 */
export function validatePPERecognition(entry) {
  const validationResults = {
    isValid: true,
    warnings: [],
    errors: []
  };
  
  const ppeAccounts = entry.lines.filter(line => 
    line.account?.subtype === 'non_current_asset' && 
    (line.account?.name?.toLowerCase().includes('equipment') ||
     line.account?.name?.toLowerCase().includes('property') ||
     line.account?.name?.toLowerCase().includes('building') ||
     line.account?.name?.toLowerCase().includes('machinery'))
  );
  
  if (ppeAccounts.length === 0) return validationResults;
  
  // Check for proper initial recognition
  ppeAccounts.forEach(line => {
    if (line.debit > 0) { // Asset acquisition
      const hasCorrespondingCredit = entry.lines.some(otherLine => 
        otherLine !== line && otherLine.credit > 0
      );
      
      if (!hasCorrespondingCredit) {
        validationResults.errors.push(
          `PPE acquisition for ${line.account?.name} must have corresponding credit entry`
        );
        validationResults.isValid = false;
      }
    }
  });
  
  return validationResults;
}

/**
 * General PFRS compliance check for journal entries
 */
export function validatePFRSCompliance(entry, accounts) {
  const results = {
    isValid: true,
    warnings: [],
    errors: [],
    suggestions: []
  };
  
  // Add account information to entry lines
  const enrichedEntry = {
    ...entry,
    lines: entry.lines.map(line => ({
      ...line,
      account: accounts.find(acc => acc._id === line.accountId)
    }))
  };
  
  // Run all PFRS validations
  const revenueValidation = validateRevenueRecognition(enrichedEntry);
  const cashFlowValidation = validateCashFlowClassification(enrichedEntry);
  const ppeValidation = validatePPERecognition(enrichedEntry);
  
  // Combine results
  results.warnings.push(...revenueValidation.warnings);
  results.errors.push(...revenueValidation.errors);
  
  results.warnings.push(...cashFlowValidation.warnings);
  results.errors.push(...cashFlowValidation.errors);
  
  results.warnings.push(...ppeValidation.warnings);
  results.errors.push(...ppeValidation.errors);
  
  if (cashFlowValidation.suggestedClassification) {
    results.suggestions.push(
      `Suggested cash flow classification: ${cashFlowValidation.suggestedClassification}`
    );
  }
  
  results.isValid = results.errors.length === 0;
  
  return results;
}

/**
 * BIR (Bureau of Internal Revenue) audit trail validation
 */
export function validateBIRAuditTrail(entry) {
  const validationResults = {
    isValid: true,
    warnings: [],
    errors: []
  };
  
  // Check for required fields for BIR compliance
  if (!entry.reference) {
    validationResults.warnings.push('Consider adding reference number for BIR audit trail');
  }
  
  if (!entry.description || entry.description.length < 10) {
    validationResults.warnings.push('Description should be detailed for BIR audit requirements');
  }
  
  // Check for proper documentation
  if (entry.totalDebit > 50000 || entry.totalCredit > 50000) { // Significant amounts
    validationResults.warnings.push(
      'Large transaction amounts should have proper supporting documentation for BIR compliance'
    );
  }
  
  return validationResults;
}
