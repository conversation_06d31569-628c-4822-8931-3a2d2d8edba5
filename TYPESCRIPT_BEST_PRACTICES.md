# TypeScript Best Practices for XYZ Finance

This document outlines TypeScript best practices to prevent implicit `any` types and maintain type safety throughout the codebase.

## 🚫 Common Issues Fixed

### 1. Implicit `any` in Function Parameters

**❌ Before (Error-prone):**
```typescript
// Parameter 'account' implicitly has an 'any' type
chartOfAccounts.filter(account => account.type === type)
```

**✅ After (Type-safe):**
```typescript
// Explicit type annotation
chartOfAccounts.filter((account: Account) => account.type === type)
```

### 2. Untyped Error Handling

**❌ Before (Error-prone):**
```typescript
try {
  await someOperation();
} catch (error) {
  alert(`Error: ${error.message}`); // error is 'any'
}
```

**✅ After (Type-safe):**
```typescript
try {
  await someOperation();
} catch (error: unknown) {
  const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
  alert(`Error: ${errorMessage}`);
}
```

### 3. Missing Button Type Attributes

**❌ Before (Accessibility issue):**
```tsx
<button onClick={handleClick}>Click me</button>
```

**✅ After (Accessible):**
```tsx
<button type="button" onClick={handleClick}>Click me</button>
```

## 📋 Type Safety Checklist

### Interface Definitions
- [ ] All data structures have proper interfaces
- [ ] Optional properties use `?` syntax
- [ ] Union types are properly defined
- [ ] Array types use `Type[]` syntax

### Function Signatures
- [ ] All function parameters have explicit types
- [ ] Return types are specified for complex functions
- [ ] Async functions return `Promise<Type>`
- [ ] Event handlers have proper event types

### State Management
- [ ] useState hooks have explicit type parameters
- [ ] State interfaces are defined for complex state
- [ ] Reducer actions have proper type unions

### API Integration
- [ ] Query results are properly typed
- [ ] Mutation parameters have interfaces
- [ ] API responses have type assertions

## 🛠️ TypeScript Configuration

Our `tsconfig.json` includes strict type checking:

```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noUncheckedIndexedAccess": true,
    "forceConsistentCasingInFileNames": true
  }
}
```

## 🔍 ESLint Rules

Key ESLint rules for TypeScript safety:

- `@typescript-eslint/no-explicit-any`: Prevents `any` usage
- `@typescript-eslint/explicit-function-return-type`: Requires return types
- `@typescript-eslint/no-unused-vars`: Catches unused variables
- `@typescript-eslint/prefer-as-const`: Enforces const assertions
- `@typescript-eslint/strict-boolean-expressions`: Prevents truthy/falsy bugs

## 📝 Code Examples

### Proper Interface Definition
```typescript
interface Account {
  _id: string;
  code: string;
  name: string;
  type: "asset" | "liability" | "equity" | "revenue" | "expense";
  subtype?: string;
  isActive: boolean;
}
```

### Type-safe Array Operations
```typescript
// Use const assertion for literal arrays
const accountTypes = ["asset", "liability", "equity", "revenue", "expense"] as const;
type AccountType = typeof accountTypes[number];

// Type array operations explicitly
accounts.map((account: Account) => ({ ...account, formatted: true }))
```

### Proper Event Handling
```typescript
const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
  setDateRange(prev => ({ ...prev, startDate: e.target.value }));
};
```

### Type-safe API Queries
```typescript
const balanceSheet = useQuery(
  api.balanceSheet.generateBalanceSheet,
  selectedReport === "balance_sheet" ? {
    asOfDate: new Date(dateRange.endDate).getTime(),
    includeZeroBalances: false
  } : "skip"
) as BalanceSheetData | undefined;
```

## 🚀 Development Workflow

1. **Write interfaces first** - Define data structures before implementation
2. **Use type assertions carefully** - Only when you're certain of the type
3. **Leverage TypeScript inference** - Let TypeScript infer when possible
4. **Test with strict mode** - Always develop with strict TypeScript settings
5. **Use ESLint** - Run linting before commits

## 🔧 Tools and Extensions

### VS Code Extensions
- TypeScript Importer
- TypeScript Hero
- Error Lens
- ESLint

### Build Tools
- TypeScript compiler with strict mode
- ESLint with TypeScript rules
- Prettier for consistent formatting

## 📚 Resources

- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [React TypeScript Cheatsheet](https://react-typescript-cheatsheet.netlify.app/)
- [ESLint TypeScript Rules](https://typescript-eslint.io/rules/)

---

**Remember**: Type safety is not just about preventing errors—it's about making code more maintainable, readable, and self-documenting.
